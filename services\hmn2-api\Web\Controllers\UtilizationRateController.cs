using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.UtilizationRate;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// UtilizationRate 控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class UtilizationRateController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    /// <summary>
    /// 查詢使用率
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpGet("utilizationrates")]
    public async Task<IActionResult> RetrieveUtilizationRate([FromQuery] RetrieveUtilizationRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var query = _dataAccessService.Fetch<UtilizationRate>(e => true);

        // 應用篩選條件
        query = query.Where(e => e.AppCode == _user.AppCode);

        if (!string.IsNullOrEmpty(param.ObjectCodes))
        {
            var objectCodes = param.ObjectCodes.Split(',');
            query = query.Where(e => objectCodes.Contains(e.ObjectCode));
        }

        if (!string.IsNullOrEmpty(param.UsageDepartCodes))
        {
            var usageDepartCodes = param.UsageDepartCodes.Split(',');
            query = query.Where(e => usageDepartCodes.Contains(e.UsageDepartCode));
        }

        if (!string.IsNullOrEmpty(param.GroupCodes))
        {
            var groupCodes = param.GroupCodes.Split(',');
            query = query.Where(e => groupCodes.Contains(e.GroupCode));
        }

        if (!string.IsNullOrEmpty(param.Pids))
        {
            var pids = param.Pids.Split(',');
            query = query.Where(e => pids.Contains(e.Pid));
        }

        if (!string.IsNullOrEmpty(param.CreateUserAccount))
            query = query.Where(e => e.CreateUserAccount == param.CreateUserAccount);

        if (!string.IsNullOrEmpty(param.UtilizationDay))
        {
            // 如果 param.UtilizationDay 包含 "," 代表是時間區間
            if (param.UtilizationDay.Contains(","))
            {
                string[] timeBounds = param.UtilizationDay.Split(',');
                DateTime lowerBound = DateTime.Parse(timeBounds[0]);
                DateTime upperBound = DateTime.Parse(timeBounds[1]);
                query = query.Where(e => e.UtilizationDay >= lowerBound && e.UtilizationDay <= upperBound);
            }
            else
            {
                // 如果  param.UtilizationDay 只包含年份，則查詢該年份的所有資料
                if (param.UtilizationDay.Length == 4)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay));
                }
                // 如果  param.UtilizationDay 只包含年月，則查詢該年月的所有資料
                else if (param.UtilizationDay.Length == 7)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay.Substring(0, 4)) && e.UtilizationDay.Month == int.Parse(param.UtilizationDay.Substring(5, 2)));
                }
                // 如果  param.UtilizationDay 只包含年月日，則查詢該日期的所有資料
                else if (param.UtilizationDay.Length == 10)
                {
                    query = query.Where(e => e.UtilizationDay == DateTime.Parse(param.UtilizationDay));
                }
            }
        }

        // 分頁處理
        int page = int.TryParse(param.page, out int p) ? p : 1;
        int size = int.TryParse(param.size, out int s) ? s : 0;
        int skip = (page - 1) * size;

        var totalCount = await query.CountAsync();
        var data = await query.Skip(skip).Take(size).ToListAsync();

        _logService.Logging("info", logActionName, requestUUID, "UtilizationRate Data retrieve done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                totalCount = totalCount,
                page = page,
                size = size,
                data = data
            }
        });
    }

    /// <summary>
    /// 查詢對象群組與單位使用率
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpGet("utilizationrates/groupUsage")]
    public async Task<IActionResult> RetrieveUtilizationRateByGroupUsage([FromQuery] RetrieveUtilizationRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var query = _dataAccessService.Fetch<UtilizationRate>(e => true);

        // 應用篩選條件
        query = query.Where(e => e.AppCode == _user.AppCode);

        if (!string.IsNullOrEmpty(param.ObjectCodes))
        {
            var objectCodes = param.ObjectCodes.Split(',');
            query = query.Where(e => objectCodes.Contains(e.ObjectCode));
        }

        if (!string.IsNullOrEmpty(param.UsageDepartCodes))
        {
            var usageDepartCodes = param.UsageDepartCodes.Split(',');
            query = query.Where(e => usageDepartCodes.Contains(e.UsageDepartCode));
        }

        if (!string.IsNullOrEmpty(param.GroupCodes))
        {
            var groupCodes = param.GroupCodes.Split(',');
            query = query.Where(e => groupCodes.Contains(e.GroupCode));
        }

        if (!string.IsNullOrEmpty(param.Pids))
        {
            var pids = param.Pids.Split(',');
            query = query.Where(e => pids.Contains(e.Pid));
        }

        if (!string.IsNullOrEmpty(param.CreateUserAccount))
            query = query.Where(e => e.CreateUserAccount == param.CreateUserAccount);

        if (!string.IsNullOrEmpty(param.UtilizationDay))
        {
            // 如果 param.UtilizationDay 包含 "," 代表是時間區間
            if (param.UtilizationDay.Contains(","))
            {
                string[] timeBounds = param.UtilizationDay.Split(',');
                DateTime lowerBound = DateTime.Parse(timeBounds[0]);
                DateTime upperBound = DateTime.Parse(timeBounds[1]);
                query = query.Where(e => e.UtilizationDay >= lowerBound && e.UtilizationDay <= upperBound);
            }
            else
            {
                // 如果  param.UtilizationDay 只包含年份，則查詢該年份的所有資料
                if (param.UtilizationDay.Length == 4)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay));
                }
                // 如果  param.UtilizationDay 只包含年月，則查詢該年月的所有資料
                else if (param.UtilizationDay.Length == 7)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay.Substring(0, 4)) && e.UtilizationDay.Month == int.Parse(param.UtilizationDay.Substring(5, 2)));
                }
                // 如果  param.UtilizationDay 只包含年月日，則查詢該日期的所有資料
                else if (param.UtilizationDay.Length == 10)
                {
                    query = query.Where(e => e.UtilizationDay == DateTime.Parse(param.UtilizationDay));
                }
            }
        }

        var data = await query.ToListAsync();

        var objectGroups = await _dataAccessService.Fetch<ObjectGroup>(e => e.AppCode == _user.AppCode).ToListAsync();
        var departments = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode).ToListAsync();

        // 取得所有 data 中的 usageDepartCode
        var usageDepartCodeList = data.Select(e => e.UsageDepartCode).Distinct().ToList();

        // 取得所有 usageDepartCodeList 的 deptCode 對應 departName 的 Dict
        var usageDepartCodeDict = departments.Where(e => usageDepartCodeList.Contains(e.DeptCode)).ToDictionary(e => e.DeptCode, e => e.DeptName);

        var outData = new List<Dictionary<string, string>>();

        var objectGroupsUsage = data.GroupBy(e => e.GroupCode);
        foreach (var group in objectGroupsUsage)
        {
            Dictionary<string, string> outDataItem = new Dictionary<string, string>();

            var groupCode = group.Key;
            var groupName = objectGroups.FirstOrDefault(e => e.GroupCode == groupCode)?.GroupName;
            var utilizationratesByGroup = group.ToList();

            // outDataItem["GroupCode"] = groupCode;
            outDataItem["對象群組"] = groupName ?? "未分類";

            // 將 utilizationratesByGroup 依照 usageDepartCode 進行分組
            var objectGroupUsageByUsageDepartCode = utilizationratesByGroup.GroupBy(e => e.UsageDepartCode);
            foreach (var usageDepartGroup in objectGroupUsageByUsageDepartCode)
            {
                var usageDepartCode = usageDepartGroup.Key;
                var usageDepartName = usageDepartCodeDict[usageDepartCode];

                var utilizationratesByUsageDepartCode = usageDepartGroup.ToList();

                // 計算 utilizationratesByUsageDepartCode 中的 UtilizationRateValue 平均值
                var utilizationRateValueAvg = utilizationratesByUsageDepartCode.Average(e => e.UtilizationRateValue);

                // 將 utilizationRateValueAvg 轉換成百分比字串 XX.XX%
                var utilizationRateValueAvgPercentage = utilizationRateValueAvg * 100;

                outDataItem[usageDepartName] = utilizationRateValueAvgPercentage.ToString("F2") + "%";
            }

            // 將 usageDepartCodeDict 對應的 deptName 不存在 outDataItem 中的 key，則新增一個 key，value 為 0.00%
            foreach (var usageDepartCode in usageDepartCodeDict.Keys)
            {
                if (!outDataItem.ContainsKey(usageDepartCodeDict[usageDepartCode]))
                {
                    outDataItem[usageDepartCodeDict[usageDepartCode]] = "0.00%";
                }
            }

            outData.Add(outDataItem);
        }

        // 總筆數
        var totalCount = outData.Count();

        // 分頁處理
        int page = int.TryParse(param.page, out int p) ? p : 1;
        int size = int.TryParse(param.size, out int s) ? s : 0;
        int skip = (page - 1) * size;

        // 分頁
        var pagedData = size == 0 ? outData : outData.Skip(skip).Take(size).ToList();

        _logService.Logging("info", logActionName, requestUUID, "UtilizationRate Data retrieve done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                totalCount = totalCount,
                page = page,
                size = size,
                data = pagedData
            }
        });
    }

    /// <summary>
    /// 查詢單位與設備明細使用率
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpGet("utilizationrates/departDeviceUsage")]
    public async Task<IActionResult> RetrieveUtilizationRateByDepartDeviceUsage([FromQuery] RetrieveUtilizationRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var query = _dataAccessService.Fetch<UtilizationRate>(e => true);

        // 應用篩選條件
        query = query.Where(e => e.AppCode == _user.AppCode);

        if (!string.IsNullOrEmpty(param.ObjectCodes))
        {
            var objectCodes = param.ObjectCodes.Split(',');
            query = query.Where(e => objectCodes.Contains(e.ObjectCode));
        }

        if (!string.IsNullOrEmpty(param.UsageDepartCodes))
        {
            var usageDepartCodes = param.UsageDepartCodes.Split(',');
            query = query.Where(e => usageDepartCodes.Contains(e.UsageDepartCode));
        }

        if (!string.IsNullOrEmpty(param.GroupCodes))
        {
            var groupCodes = param.GroupCodes.Split(',');
            query = query.Where(e => groupCodes.Contains(e.GroupCode));
        }

        if (!string.IsNullOrEmpty(param.Pids))
        {
            var pids = param.Pids.Split(',');
            query = query.Where(e => pids.Contains(e.Pid));
        }

        if (!string.IsNullOrEmpty(param.CreateUserAccount))
            query = query.Where(e => e.CreateUserAccount == param.CreateUserAccount);

        if (!string.IsNullOrEmpty(param.UtilizationDay))
        {
            // 如果 param.UtilizationDay 包含 "," 代表是時間區間
            if (param.UtilizationDay.Contains(","))
            {
                string[] timeBounds = param.UtilizationDay.Split(',');
                DateTime lowerBound = DateTime.Parse(timeBounds[0]);
                DateTime upperBound = DateTime.Parse(timeBounds[1]);
                query = query.Where(e => e.UtilizationDay >= lowerBound && e.UtilizationDay <= upperBound);
            }
            else
            {
                // 如果  param.UtilizationDay 只包含年份，則查詢該年份的所有資料
                if (param.UtilizationDay.Length == 4)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay));
                }
                // 如果  param.UtilizationDay 只包含年月，則查詢該年月的所有資料
                else if (param.UtilizationDay.Length == 7)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay.Substring(0, 4)) && e.UtilizationDay.Month == int.Parse(param.UtilizationDay.Substring(5, 2)));
                }
                // 如果  param.UtilizationDay 只包含年月日，則查詢該日期的所有資料
                else if (param.UtilizationDay.Length == 10)
                {
                    query = query.Where(e => e.UtilizationDay == DateTime.Parse(param.UtilizationDay));
                }
            }
        }

        var data = await query.ToListAsync();

        var departments = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode).ToListAsync();
        var devices = await _dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(e => e.AppCode == _user.AppCode).ToListAsync();

        var outData = new List<Dictionary<string, string>>();

        var deviceUsage = data.GroupBy(e => e.Pid);
        foreach (var group in deviceUsage)
        {
            Dictionary<string, string> outDataItem = new Dictionary<string, string>();

            var pid = group.Key;
            var utilizationratesByPid = group.ToList();

            var deviceName = devices.FirstOrDefault(e => e.Pid == pid)?.Name;
            outDataItem["名稱"] = deviceName ?? "未知設備";

            // 將 utilizationratesByPid 依照 usageDepartCode 進行分組
            var deviceUsageByUsageDepartCode = utilizationratesByPid.GroupBy(e => e.UsageDepartCode);
            foreach (var usageDepartGroup in deviceUsageByUsageDepartCode)
            {
                var usageDepartCode = usageDepartGroup.Key;
                var usageDepartName = departments.FirstOrDefault(e => e.DeptCode == usageDepartCode)?.DeptName;
                outDataItem["單位"] = usageDepartName ?? "未知單位";

                var utilizationratesByUsageDepartCode = usageDepartGroup.ToList();

                // 將 utilizationratesByUsageDepartCode 依照 UtilizationDay 進行排序
                utilizationratesByUsageDepartCode = utilizationratesByUsageDepartCode.OrderBy(e => e.UtilizationDay).ToList();

                string startDate = utilizationratesByUsageDepartCode.First().UtilizationDay.ToString("yyyy-MM-dd");
                string endDate = utilizationratesByUsageDepartCode.Last().UtilizationDay.ToString("yyyy-MM-dd");
                outDataItem["起始日期"] = startDate;
                outDataItem["截止日期"] = endDate;

                // 計算 utilizationratesByUsageDepartCode 中的 DayTotalSec 總和
                var dayTotalSecSum = utilizationratesByUsageDepartCode.Sum(e => e.DayTotalSec);

                // 將 dayTotalSecSum 轉換成 HH:mm:ss 的格式
                var dayTotalSecSumHHmmss = TimeSpan.FromSeconds(dayTotalSecSum).ToString(@"hh\:mm\:ss");
                outDataItem["使用時間"] = dayTotalSecSumHHmmss;

                // 計算 utilizationratesByUsageDepartCode 中的 UtilizationRateValue 平均值
                var utilizationRateValueAvg = utilizationratesByUsageDepartCode.Average(e => e.UtilizationRateValue);

                // 將 utilizationRateValueAvg 轉換成百分比字串 XX.XX%
                var utilizationRateValueAvgPercentage = utilizationRateValueAvg * 100;

                outDataItem["稼動率"] = utilizationRateValueAvgPercentage.ToString("F2") + "%";
            }

            outData.Add(outDataItem);
        }

        // 總筆數
        var totalCount = outData.Count();

        // 分頁處理
        int page = int.TryParse(param.page, out int p) ? p : 1;
        int size = int.TryParse(param.size, out int s) ? s : 0;
        int skip = (page - 1) * size;

        // 分頁
        var pagedData = size == 0 ? outData : outData.Skip(skip).Take(size).ToList();

        _logService.Logging("info", logActionName, requestUUID, "UtilizationRate Data retrieve done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                totalCount = totalCount,
                page = page,
                size = size,
                data = pagedData
            }
        });
    }
    
    /// <summary>
    /// 查詢設使用紀錄
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpGet("utilizationrates/deviceUsage")]
    public async Task<IActionResult> RetrieveUtilizationRateByDeviceUsage([FromQuery] RetrieveUtilizationRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var query = _dataAccessService.Fetch<UtilizationRate>(e => true);

        // 應用篩選條件
        query = query.Where(e => e.AppCode == _user.AppCode);

        if (!string.IsNullOrEmpty(param.ObjectCodes))
        {
            var objectCodes = param.ObjectCodes.Split(',');
            query = query.Where(e => objectCodes.Contains(e.ObjectCode));
        }

        if (!string.IsNullOrEmpty(param.UsageDepartCodes))
        {
            var usageDepartCodes = param.UsageDepartCodes.Split(',');
            query = query.Where(e => usageDepartCodes.Contains(e.UsageDepartCode));
        }

        if (!string.IsNullOrEmpty(param.GroupCodes))
        {
            var groupCodes = param.GroupCodes.Split(',');
            query = query.Where(e => groupCodes.Contains(e.GroupCode));
        }

        if (!string.IsNullOrEmpty(param.Pids))
        {
            var pids = param.Pids.Split(',');
            query = query.Where(e => pids.Contains(e.Pid));
        }

        if (!string.IsNullOrEmpty(param.CreateUserAccount))
            query = query.Where(e => e.CreateUserAccount == param.CreateUserAccount);

        if (!string.IsNullOrEmpty(param.UtilizationDay))
        {
            // 如果 param.UtilizationDay 包含 "," 代表是時間區間
            if (param.UtilizationDay.Contains(","))
            {
                string[] timeBounds = param.UtilizationDay.Split(',');
                DateTime lowerBound = DateTime.Parse(timeBounds[0]);
                DateTime upperBound = DateTime.Parse(timeBounds[1]);
                query = query.Where(e => e.UtilizationDay >= lowerBound && e.UtilizationDay <= upperBound);
            }
            else
            {
                // 如果  param.UtilizationDay 只包含年份，則查詢該年份的所有資料
                if (param.UtilizationDay.Length == 4)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay));
                }
                // 如果  param.UtilizationDay 只包含年月，則查詢該年月的所有資料
                else if (param.UtilizationDay.Length == 7)
                {
                    query = query.Where(e => e.UtilizationDay.Year == int.Parse(param.UtilizationDay.Substring(0, 4)) && e.UtilizationDay.Month == int.Parse(param.UtilizationDay.Substring(5, 2)));
                }
                // 如果  param.UtilizationDay 只包含年月日，則查詢該日期的所有資料
                else if (param.UtilizationDay.Length == 10)
                {
                    query = query.Where(e => e.UtilizationDay == DateTime.Parse(param.UtilizationDay));
                }
            }
        }

        var data = await query.ToListAsync();

        var departments = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode).ToListAsync();
        var devices = await _dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(e => e.AppCode == _user.AppCode).ToListAsync();

        var outData = new List<Dictionary<string, string>>();

        var deviceUsage = data.GroupBy(e => e.Pid);
        foreach (var group in deviceUsage)
        {
            var pid = group.Key;
            var utilizationratesByPid = group.ToList();

            var deviceName = devices.FirstOrDefault(e => e.Pid == pid)?.Name;

            // 將 utilizationratesByPid 依照 usageDepartCode 進行分組
            var deviceUsageByUsageDepartCode = utilizationratesByPid.GroupBy(e => e.UsageDepartCode);
            foreach (var usageDepartGroup in deviceUsageByUsageDepartCode)
            {
                var usageDepartCode = usageDepartGroup.Key;
                var usageDepartName = departments.FirstOrDefault(e => e.DeptCode == usageDepartCode)?.DeptName;
                
                var utilizationratesByUsageDepartCode = usageDepartGroup.ToList();

                // 將 utilizationratesByUsageDepartCode 依照 UtilizationDay 進行排序
                utilizationratesByUsageDepartCode = utilizationratesByUsageDepartCode.OrderBy(e => e.UtilizationDay).ToList();

                foreach (var utilizationRate in utilizationratesByUsageDepartCode)
                {
                    Dictionary<string, string> outDataItem = new Dictionary<string, string>();
                    outDataItem["名稱"] = deviceName ?? "未知設備";
                    outDataItem["單位"] = usageDepartName ?? "未知單位";
                    outDataItem["日期"] = utilizationRate.UtilizationDay.ToString("yyyy-MM-dd");
                    outDataItem["使用時間"] = TimeSpan.FromSeconds(utilizationRate.DayTotalSec).ToString(@"hh\:mm\:ss");  

                    outData.Add(outDataItem);
                }
            }
        }

        // 總筆數
        var totalCount = outData.Count();

        // 分頁處理
        int page = int.TryParse(param.page, out int p) ? p : 1;
        int size = int.TryParse(param.size, out int s) ? s : 0;
        int skip = (page - 1) * size;

        // 分頁
        var pagedData = size == 0 ? outData : outData.Skip(skip).Take(size).ToList();

        _logService.Logging("info", logActionName, requestUUID, "UtilizationRate Data retrieve done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                totalCount = totalCount,
                page = page,
                size = size,
                data = pagedData
            }
        });
    }
}
