using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Web.Models.Controller.CannedMessage;
using Web.Models.Controller;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 罐頭訊息
/// </summary>
[Route("[controller]")]
[Authorize]
public class CannedMessageController(IDataAccessService dataAccessService,
                                    ICredentialService credentialService,
                                    IRequestContextService requestContextService,
                                    ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{   
    /// <summary>
    /// 新增罐頭訊息檢核
    /// </summary>
    [HttpPost("cannedMessages/validate")]
    [RequestParamListDuplicate("CannedCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateCannedMessage([FromBody] List<CreateCannedMessage> paramList)
    {
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增罐頭訊息
    /// </summary>
    [HttpPost("cannedMessages")]
    [RequestParamListDuplicate("CannedCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateCannedMessage([FromBody] List<CreateCannedMessage> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (CreateCannedMessage cm in paramList)
        {
            CannedMessage cannedMessage = new CannedMessage
            {
                AppCode = _user.AppCode,
                CannedType = cm.CannedType,
                CannedCode = cm.CannedCode,
                Message = cm.Message,
                Enable = cm.Enable == "Y",
                SystemDefault = cm.SystemDefault == "Y",
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            await _dataAccessService.CreateAsync<CannedMessage>(cannedMessage);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "CannedMessage Data append done.");

        return StatusCode(StatusCodes.Status201Created, new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        });
    }

    [HttpPatch("cannedMessages")]
    [RequestParamListDuplicate("CannedCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateCannedMessage([FromBody] List<UpdateCannedMessage> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateCannedMessage param in paramList)
        {
            CannedMessage cannedMessage = _dataAccessService.Fetch<CannedMessage>(e => e.AppCode == _user.AppCode && e.CannedCode == param.CannedCode).AsTracking().First();

            List<string> updateField = new List<string>();

            if (param.CannedType != null)
            {
                updateField.Add("CannedType");
                cannedMessage.CannedType = param.CannedType;
            }

            if (param.Message != null)
            {
                updateField.Add("Message");
                cannedMessage.Message = param.Message;
            }

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                cannedMessage.Enable = param.Enable == "Y";
            }

            if (param.SystemDefault != null)
            {
                updateField.Add("SystemDefault");
                cannedMessage.SystemDefault = param.SystemDefault == "Y";
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            cannedMessage.ModifyDate = DateTime.Now;
            cannedMessage.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<CannedMessage>(cannedMessage, updateField.ToArray());
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "CannedMessage Data update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    [HttpDelete("cannedMessages")]
    [RequestParamListDuplicate("CannedCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteCannedMessage([FromBody] List<DeleteCannedMessage> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (DeleteCannedMessage cm in paramList)
        {
            await _dataAccessService.DeleteAsync<CannedMessage>(e => e.AppCode == _user.AppCode && e.CannedCode == cm.CannedCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "CannedMessage Data delete done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    [HttpGet("cannedMessages")]
    public async Task<IActionResult> RetrieveCannedMessage([FromQuery] RetrieveCannedMessage queryParam)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(queryParam.page, out int page);
        bool sizeParseResult = int.TryParse(queryParam.size, out int size);

        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;

        string sort = string.IsNullOrEmpty(queryParam.sort) ? "ModifyDate:desc" : queryParam.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var query = _dataAccessService.Fetch<CannedMessage>(x => x.AppCode == _user.AppCode)
            .Where(x => (queryParam.CannedType == null || x.CannedType.ToUpper().Contains(queryParam.CannedType.ToUpper()))
                     && (queryParam.CannedCode == null || x.CannedCode.ToUpper().Contains(queryParam.CannedCode.ToUpper()))
                     && (queryParam.Message == null || x.Message.ToUpper().Contains(queryParam.Message.ToUpper()))
                     && (queryParam.Enable == null || x.Enable == (queryParam.Enable == "Y"))
                     && (queryParam.SystemDefault == null || x.SystemDefault == (queryParam.SystemDefault == "Y")));

        int recordTotal = await query.CountAsync();

        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        var queryResult = size == 0 
            ? await query.ToListAsync()
            : await query.Skip(skip).Take(size).ToListAsync();
        
        var recordList = queryResult.Select(x => new
        {
            x.Id,
            x.AppCode,
            x.CannedType,
            x.CannedCode,
            x.Message,
            Enable = x.Enable == true ? "Y" : "N",
            SystemDefault = x.SystemDefault == true ? "Y" : "N",
            x.CreateUserAccount,
            x.CreateDate,
            x.ModifyUserAccount,
            x.ModifyDate
        }).ToList();

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        });
    }
}