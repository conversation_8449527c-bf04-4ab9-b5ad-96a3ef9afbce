﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Location;

public class RetrieveLocation
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string LocCode { get; set; }
    public string LocName { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string PlaneCode { get; set; }
    public string PlaneName { get; set; }
    public string Enable { get; set; }

}

public class CreateLocation
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "LocCode")]
    [Unique("AreaCode", "Location", "LocCode", ErrorMessage = Constants.ErrorCode.Unique + "LocCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "LocCode")]
    public string LocCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "LocName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "LocName")]
    public string LocName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingCode")]
    [Exists("AreaCode", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [ListAllExists("AreaCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "SIDList")]
    [EqualsPropertyValue("AreaCode", "Station", "SID", "PlaneCode", "PlaneCode", true, ErrorMessage = Constants.ErrorCode.Invalid + "SIDList.SID")]
    public List<string> SIDList { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Utilization")]
    public string? Utilization { get; set; }
}

public class UpdateLocation
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "LocCode")]
    [Exists("AreaCode", "Location", "LocCode", ErrorMessage = Constants.ErrorCode.NotFound + "LocCode")]
    [HasReferenceWhenEquals("Enable", "N", "", "Station", "RegionCode", ErrorMessage = Constants.ErrorCode.Reference + "LocCode")]
    public string LocCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "LocName")]
    public string LocName { get; set; }

    [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [ListAllExists("", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "SIDList")]
    [EqualsPropertyValue("AreaCode", "Station", "SID", "PlaneCode", "PlaneCode", true, ErrorMessage = Constants.ErrorCode.Invalid + "SIDList.SID")]
    public List<string> SIDList { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Utilization")]
    public string? Utilization { get; set; }
}

public class DeleteLocation
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "LocCode")]
    [Exists("AreaCode", "Location", "LocCode", ErrorMessage = Constants.ErrorCode.NotFound + "LocCode")]
    [HasReference("", "Station", "RegionCode", ErrorMessage = Constants.ErrorCode.Reference + "LocCode")]
    public string LocCode { get; set; }

}
