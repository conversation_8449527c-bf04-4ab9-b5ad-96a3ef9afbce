using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class UtilizationRate
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string ObjectCode { get; set; } = null!;

    public string UsageDepartCode { get; set; } = null!;

    public string? GroupCode { get; set; }

    public string Pid { get; set; } = null!;

    public DateTime UtilizationDay { get; set; }

    public int DayTotalSec { get; set; }

    public double UtilizationRateValue { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }
}
