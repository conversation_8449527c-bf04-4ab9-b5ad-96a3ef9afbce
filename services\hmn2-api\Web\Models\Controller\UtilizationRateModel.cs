using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.UtilizationRate;

public class RetrieveUtilizationRate
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string ObjectCodes { get; set; }
    public string UsageDepartCodes { get; set; }
    public string GroupCodes { get; set; }
    public string Pids { get; set; }
    public string UtilizationDay { get; set; }
    public string CreateUserAccount { get; set; }
}