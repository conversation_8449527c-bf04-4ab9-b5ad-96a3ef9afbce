﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Security.Claims;
using System.Text.Json;
using Web.Models.Controller;
using Web.Models.Controller.Auth;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// 認證控制器
/// </summary>
[Route("[controller]")]
public class AuthController(IConfiguration configuration,
                            IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IUtilityService utilityService,
                            IAuthService authService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IConfiguration _configuration = configuration;
    private readonly IUtilityService _utilityService = utilityService;
    private readonly IAuthService _authService = authService;

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] Login param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 檢查ModelState是否正確
        if (!ModelState.IsValid)
        {
            _logService.Logging("error", logActionName, requestUUID, "ModelState is not valid");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return BadRequest(ModelState);
        }

        string userAccount = param.UserAccount.ToUpper();

        // 驗證使用者帳號密碼
        var (loginResult, appCode) = await _authService.Login(userAccount, param.UserPassword);

        var userLogonLog = new UserLogonLog
        {
            UserAccount = userAccount,
            CreateDate = DateTime.Now,
            ClientIp = _utilityService.GetClientIp(HttpContext),
            LogonResult = loginResult
        };

        _logService.Logging("info", logActionName, requestUUID, $"UserLogonLog==>{JsonSerializer.Serialize(userLogonLog)}");

        // 紀錄使用者登入紀錄
        await _dataAccessService.CreateAsync(userLogonLog);

        // 登入失敗，回傳401
        if (!loginResult)
        {
            var userData = await _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == userAccount).FirstOrDefaultAsync();
            var roleData = await _dataAccessService.Fetch<Role>(x => x.AppCode == _user.AppCode && x.RoleCode == userData.RoleCode).FirstOrDefaultAsync();

            if (userData?.Enable == false)
            {
                //用戶為停用狀態
                return BadRequest(new ReturnModel { httpStatus = StatusCodes.Status400BadRequest, result = false, title = "userDisable" });
            }
            else if (roleData?.Enable == false)
            {
                //角色為停用狀態
                return BadRequest(new ReturnModel { httpStatus = StatusCodes.Status400BadRequest, result = false, title = "roleDisable" });
            }
            else
            {
                return Unauthorized(new ReturnModel { httpStatus = StatusCodes.Status401Unauthorized, result = false, title = "loginFail" });
            }
        }

        // 將使用者UserAccount、UserName 及 UserClientPara 等等資料，轉換成Claims（HMNAdmin所要指定的屬性值，都在此method）
        List<Claim> claims = await _authService.GenerateClaimList(appCode, userAccount);

        // 產生ClaimsIdentity（注意！！有兩個 out 參數）
        _authService.CreatePersistentUserClaimsIdentity(claims, out ClaimsIdentity claimsIdentity, out AuthenticationProperties authProperties);

        // 使用ClaimsIdentity 產生Cookie
        await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);

        // 產生Token
        var userToken = await _authService.GenerateUserToken(appCode, userAccount);

        return Ok(new ReturnModel { httpStatus = StatusCodes.Status200OK, result = true, token = userToken });
    }

    [HttpPost("logout")]
    public async Task<IActionResult> Logout()
    {
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

        return Ok(new ReturnModel { httpStatus = StatusCodes.Status200OK, result = true });
    }

    [HttpGet("autoLogin")]
    public async Task<IActionResult> AutoLogin([FromQuery] InAutoLogin param)
    {
        // 檢查ModelState是否正確
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // 依傳入的token，找出對應的使用者
        var userList = _dataAccessService.Fetch<UserDatum>().ToList(); // 這裡將資料撈出來到記憶體中
        var user = userList.FirstOrDefault(u => (_authService.EncryptPwd(u.UserAccount) + u.UserPassword) == param.token);

        int returnHttpStatusCode = StatusCodes.Status200OK;

        // 若有找到使用者
        if (user != null)
        {
            // 將使用者UserAccount、UserName 及 UserClientPara 等等資料，轉換成Claims（HMNAdmin所要指定的屬性值，都在此method）
            List<Claim> claims = await _authService.GenerateClaimList(user.AppCode, user.UserAccount);

            // 產生ClaimsIdentity（注意！！有兩個 out 參數）
            _authService.CreatePersistentUserClaimsIdentity(claims, out ClaimsIdentity claimsIdentity, out AuthenticationProperties authProperties);

            // 使用ClaimsIdentity 產生Cookie
            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);

            returnHttpStatusCode = StatusCodes.Status200OK;
        }
        else
        {
            returnHttpStatusCode = StatusCodes.Status401Unauthorized;
        }

        return Ok(new ReturnModel { httpStatus = returnHttpStatusCode, result = user != null });
    }
}
