using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.CannedMessage;

public class RetrieveCannedMessage
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string CannedType { get; set; }
    public string CannedCode { get; set; }
    public string Message { get; set; }
    public string Enable { get; set; }
    public string SystemDefault { get; set; }
}

public class CreateCannedMessage
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CannedType")]
    [StringLength(2, ErrorMessage = Constants.ErrorCode.Length + "CannedType")]
    [RegularExpression(@"^[PR]+$", ErrorMessage = Constants.ErrorCode.Pattern + "CannedType")]
    public string CannedType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CannedCode")]
    [Unique("", "CannedMessage", "CannedCode", ErrorMessage = Constants.ErrorCode.Unique + "CannedCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "CannedCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CannedCode")]
    public string CannedCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Message")]
    [StringLength(512, ErrorMessage = Constants.ErrorCode.Length + "Message")]
    public string Message { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "SystemDefault")]
    public string SystemDefault { get; set; }
}

public class UpdateCannedMessage
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CannedCode")]
    [Exists("", "CannedMessage", "CannedCode", ErrorMessage = Constants.ErrorCode.NotFound + "CannedCode")]
    public string CannedCode { get; set; }

    [StringLength(2, ErrorMessage = Constants.ErrorCode.Length + "CannedType")]
    [RegularExpression(@"^[PR]+$", ErrorMessage = Constants.ErrorCode.Pattern + "CannedType")]
    public string CannedType { get; set; }
    
    [StringLength(512, ErrorMessage = Constants.ErrorCode.Length + "Message")]
    public string Message { get; set; }
    
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "SystemDefault")]
    public string SystemDefault { get; set; }
}

public class DeleteCannedMessage
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CannedCode")]
    [Exists("", "CannedMessage", "CannedCode", ErrorMessage = Constants.ErrorCode.NotFound + "CannedCode")]
    [HasReference("", "EventCannedMessage", "CannedCode", ErrorMessage = Constants.ErrorCode.Reference + "CannedCode")]
    [EqualsPropertyValue("", "CannedMessage", "CannedCode", "SystemDefault", "SystemDefault:False", false, ErrorMessage = Constants.ErrorCode.Invalid + "SystemDefault")]
    public string CannedCode { get; set; }
}