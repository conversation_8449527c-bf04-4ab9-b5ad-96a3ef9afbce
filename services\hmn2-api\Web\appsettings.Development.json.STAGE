{
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
	    "Microsoft.AspNetCore.SignalR": "Warning",
      "Microsoft.AspNetCore.Http.Connections.Internal.Transports.WebSocketsTransport": "Warning",
	    "Microsoft.EntityFrameworkCore.ChangeTracking": "Warning",
      "Microsoft.EntityFrameworkCore.Database": "Warning"
    }
  },
  "Cors": {
    "AllowOrigin": "*"
  },
  "ConnectionStrings": {
    "FusionS3HMNConnection": "server=************;port=8260;database=FusionS3HMN;uid=postgres;pwd=*************;"
  },
  "FusionNetParam": {
    "ApiUrl": "https://************:8080",
    "ApiVersion": "v3",
    "SecretKey": "nrsotosYCL2U0Q8X7crEEbFtD9ygETIY",
    "SelfCertificateFile": "C:\\CA\\FusionNetCA.crt"
  },
  "MQTTParam": {
    "HostIp": "************",
    "HostPort": "8885", //default: if 域名 9002 else 8201 
    "UseTLS": "Y", //default: if 域名 Y else N 
    "Enable": "N", //default: Y
    "Timeout": 5000,
    "UserName": "",
    "Password": "",
    "SelfCertificateFile": "C:\\CA\\FusionNetCA.crt"
  },
  "MQTTServerParam": {
    "HostPort": "8888",
	"ConnectionBacklog": 100,
	"MaxPendingMessagesPerClient": 1000
  },
  "AppInfo": {
    "AppCode": "in1007-mmhtaipei",
    "RequestIdHeaderName": "X-Request-ID"
  },
  "MapInfo": {
    "SectorUrl": "http://************/hmnweb/upload/img/sector/",
    "PlaneUrl": ""
  },
  "HMNAlpha": {
    "WebUrl": "http://************/hmnweb/"
  },
  "FusionHmnVasAppInfo": {
    "Name": "FusionHMN VAS",
    "Version": "0.0.4",
    "FileName": "FusionHMN VAS v0.0.4.zip"
  },
  "CameraEtl": {
    "Enable": "N",
    "WorkingDirectory": "Etls",
    "WorkingFileName": "fnc-application-hmn-camera-etl.jar"
  }
}
