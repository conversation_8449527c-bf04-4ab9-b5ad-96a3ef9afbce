using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Newtonsoft.Json;
using System.Linq;
using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Controller.ObjectDevicePosition;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

[ApiController]
[Route("api/v1/Devices")]
public class DevicePositionController : ControllerBase
{
    private readonly IDataAccessService _dataAccessService;
    private readonly IDeviceService _deviceService;

    public DevicePositionController(IDataAccessService dataAccessService, IDeviceService deviceService)
    {
        _dataAccessService = dataAccessService;
        _deviceService = deviceService;
    }

    [HttpGet("GetTrajectories")]
    public async Task<IActionResult> GetTrajectories([FromForm] string account,
                                                       [FromForm] string passwords,
                                                       [FromQuery] ODataQueryOptions<RetrieveTrajectory> param)
    {
        try
        {
            var signUser = _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == account.ToUpper() && x.Enable == true).FirstOrDefault();
            if (signUser == null || signUser.UserPassword != passwords)
            {
                return Unauthorized();
            }

            var appCode = signUser.AppCode;
            var areaCodeList = _dataAccessService.Fetch<VwUserDeptMonInfo>(x => x.AppCode == appCode && x.UserAccount == account.ToUpper())
                                                  .Select(x => x.AreaCode)
                                                  .Distinct()
                                                  .ToList();

            var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var buildingList = _dataAccessService.Fetch<Web.Repository.Models.Entities.Building>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var deptList = _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var objectQuery = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var deviceQuery = _dataAccessService.Fetch<ObjectDevice>(od => objectQuery.Any(o => o.AppCode == od.AppCode && o.ObjectCode == od.ObjectCode));

            var devicePositionList = await _deviceService.GetDevicePositionsList();
            if (devicePositionList == null)
            {
                return NotFound("No device positions found.");
            }

            

        // 檢查參數是否為空
        if (param == null)
        {
           
            ReturnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = "err.null.param"
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        var errorDetailList = new List<ErrorDetail>();

        // 檢查是否有指定單位代碼或指定對象代碼
        if (//(string.IsNullOrEmpty(param.DeptCode) && string.IsNullOrWhiteSpace(param.DeptCode)) &&
            (string.IsNullOrEmpty(param.ObjectCode) && string.IsNullOrWhiteSpace(param.ObjectCode)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.ObjectCode" });
        }

        // 檢查是否有指定開始日期
        if (param.StartDate == null || (string.IsNullOrEmpty(param.StartDate) && string.IsNullOrWhiteSpace(param.StartDate)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.StartDate" });
        }

        // 檢查是否有指定結束日期
        if (param.EndDate == null || (string.IsNullOrEmpty(param.EndDate) && string.IsNullOrWhiteSpace(param.EndDate)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.EndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errorDetailList
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        // 檢查開始日期格式是否正確(yyyy-MM-dd hh24:mi:ss)
        if (DateTime.TryParse(param.StartDate, out DateTime startDate) == false)
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.param.StartDate" });
        }

        // 檢查結束日期格式是否正確(yyyy-MM-dd hh24:mi:ss)
        if (DateTime.TryParse(param.EndDate, out DateTime endDate) == false)
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.param.EndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errorDetailList
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        // 檢查開始日期是否小於結束日期
        if (startDate > endDate)
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.param.StartDateLessEndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel { authorize = (Authorize)_user, httpStatus = StatusCodes.Status400BadRequest, result = false, data = errorDetailList };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        var trajectory = await _configurationService.GetTrajectory(_user.AppCode, param.ObjectCode, param.StartDate, param.EndDate);

        returnModel = new ReturnModel { authorize = (Authorize)_user, httpStatus = StatusCodes.Status200OK, result = true, data = trajectory };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}
