﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyVersion>2.0.1.14</AssemblyVersion>
    <FileVersion>2.0.1.14</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.Development.json.46.2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspectCore.Abstractions" Version="2.4.0" />
    <PackageReference Include="ClosedXML" Version="0.102.3" />
    <PackageReference Include="CompareNETObjects" Version="4.83.0" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
    <PackageReference Include="Microsoft.AspNetCore.OData" Version="9.1.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="MQTTnet" Version="3.1.2" />
	<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.12" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.4.4" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\lib\swd\" />
    <Folder Include="wwwroot\lib\vue\" />
  </ItemGroup>

</Project>
