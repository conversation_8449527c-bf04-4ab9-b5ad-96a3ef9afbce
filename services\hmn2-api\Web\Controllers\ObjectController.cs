﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using Web.Models.Controller.Department;
using Web.Models.Controller;
using Web.Models.Controller.Camera;
using Web.Models.Controller.Object;
using Web.Models.Controller.Plane;
using Web.Models.Controller.Role;
using Web.Models.Service;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services.Fusion;
using Web.Services;
using Web.Services.Interfaces;
using Web.Exceptions;
using System.Text;
using System.Linq;
using DocumentFormat.OpenXml.Spreadsheet;
using Web.Models.Service.Configuration;
using Web.Validation;
using Web.Constant;
using Web.Models.AppSettings;
using Microsoft.OData.Edm.Validation;

namespace Web.Controller;

public class ObjectDeviceQueryResult
{
    public string ObjectCode { get; set; }
    public string Pid { get; set; }
    public string MmWaveType { get; set; }
    public string DeviceType { get; set; }
    public string DeviceName { get; set; }
    public List<ObjectEventQueryResult> ObjectEventList { get; set; }
    public List<ObjectDeviceDetailQueryResult> ObjectDeviceDetailList { get; set; }
    public List<ResourceConfiguration> Configs { get; set; } // 可新增的屬性
    public string Battery { get; set; }                     // 可新增的屬性
}

public class ObjectDeviceDetailQueryResult
{
    public string AreaCode { get; set; }
    public string ObjectCode { get; set; }
    public string Pid { get; set; }
    public string SddResource { get; set; }
    public double? Threshold { get; set; }
    public string SddComp { get; set; }
    public int? StayOvertime { get; set; }
    public int? Duration { get; set; }
    public int? SilentInterval { get; set; }
    public string CurrentValue { get; set; } // 可新增的屬性
    public DateTime? ReportTime { get; set; } // 可新增的屬性
}

public class ObjectEventQueryResult
{
    public string ObjectCode { get; set; }
    public string Pid { get; set; }
    public string ServiceCode { get; set; }
    public string SddResource { get; set; }
    public string SddComp { get; set; }
    public string EventCode { get; set; }
    public string FenceCode { get; set; }
    public string CustomFenceCode { get; set; }
}


/// <summary>
/// Object 控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class ObjectController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IPreferenceService preferenceService,
                            IConfigurationService configurationService,
                            IMonitorService monitorService,
                            IObjectService objectService, 
                            IEventService eventService,
                            IDeviceService deviceService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly IConfigurationService _configurationService = configurationService;
    private readonly IMonitorService _monitorService = monitorService;
    private readonly IObjectService _objectService = objectService;
    private readonly IEventService _eventService = eventService;
    private readonly IDeviceService _deviceService = deviceService;

    /// 新增對象檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("objects/validate")]
    [RequestParamListDuplicate("ObjectCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> ValidateObject([FromBody] List<InCreateObject> paramList)
    {
        // 資料驗證通過，開始驗證可否成功新增到 console 的 Object
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        // 取得ObjectTypes, Fences, FenceStations, FenceAlarmGroups 後續取得相關資料欄位用
        var (objectTypeList, fenceList, fenceStationList, fenceAlarmGroupList) = await _configurationService.GetObjectSustainConfData(appCode);

        // 將三層結構各自拆開成ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent, EventFence
        var (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList, eventFenceList) = _configurationService.ExtractCreateObjectParam(appCode, paramList);

        // 要新增到Console的Object資料
        List<PostFusionObjectInput> postFusionObjectInputs = [];

        foreach (var objectData in objectDataList)
        {
            PostFusionObjectInput addObjectInput = new() 
            {
                code = objectData.ObjectCode,
                type = objectTypeList.FirstOrDefault(e=>e.ObjectTypeCode == objectData.ObjectType)?.FusionCoreValue??"",
                name = objectData.Name,
                devicePids = objectDeviceList.Where(e=>e.ObjectCode == objectData.ObjectCode).Select(e=>e.Pid).ToArray()
            };

            postFusionObjectInputs.Add(addObjectInput);
        }

        // 可否成功新增到 console 的 Object
        List<PostAPIResult> addObjectResults = await _objectService.AddObjectValidation(postFusionObjectInputs);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> addObjectErrors = addObjectResults
            .AsParallel()
            .Select((result, i) => new
            {
                Index = i,
                Result = result
            })
            .Where(x => x.Result.errors?.Any() == true)
            .Select(x => new ReturnError
            {
                index = x.Index,
                code = x.Result.code,
                errors = x.Result.errors
                    .Select((error, errorIndex) => new ErrorDetail
                    {
                        index = errorIndex,
                        error = Constants.ErrorCode.FusionError,
                        code = null,
                        message = null,
                        innerMsg = error.error,
                        details = []
                    })
                    .ToList()
            })
            .ToList();

        // 若有錯誤，記錄Log並回傳
        if (addObjectErrors.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(addObjectErrors));
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = addObjectErrors
            };

            _logService.Logging("info", logActionName, requestUUID, "End");

            return StatusCode(returnModel.httpStatus, returnModel);
        }
        else
        {
            return Ok(new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status200OK,
                result = true,
                data = new List<ReturnError>()
            });
        }
    }

    /// <summary>
    /// 新增對象
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6225
    /// 新增對象在Spec. V1.012中提到的相關檢查與動作
    /// https://tpe-jira2.fihtdc.com/secure/attachment/2154367/screenshot-1.png
    /// </spec>
    [HttpPost("objects")]
    [RequestParamListDuplicate("ObjectCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateObject([FromBody] List<InCreateObject> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        // 資料檢核成功，開始新增
        _logService.Logging("info", logActionName, requestUUID, "Object Data Validated, start append.");

        // 取得ObjectTypes, Fences, FenceStations, FenceAlarmGroups 後續取得相關資料欄位用
        var (objectTypeList, fenceList, fenceStationList, fenceAlarmGroupList) = await _configurationService.GetObjectSustainConfData(appCode);

        // 將三層結構各自拆開成ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent, EventFence
        var (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList, eventFenceList) = _configurationService.ExtractCreateObjectParam(appCode, paramList);

        // 要新增到Console的Object資料
        List<PostFusionObjectInput> postFusionObjectInputs = [];

        foreach (var objectData in objectDataList)
        {
            // 若ObjectCode已存在，則刪除原有資料
            await _dataAccessService.DeleteAsync<ObjectDatum>(e => e.AppCode == appCode && e.AreaCode == objectData.AreaCode && e.ObjectCode == objectData.ObjectCode);
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.ObjectDevice>(e => e.AppCode == appCode && e.AreaCode == objectData.AreaCode && e.ObjectCode == objectData.ObjectCode);
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.ObjectDeviceDetail>(e => e.AppCode == appCode && e.AreaCode == objectData.AreaCode && e.ObjectCode == objectData.ObjectCode);

            PostFusionObjectInput addObjectInput = new() 
            {
                code = objectData.ObjectCode,
                type = objectTypeList.FirstOrDefault(e=>e.ObjectTypeCode == objectData.ObjectType)?.FusionCoreValue??"",
                name = objectData.Name,
                devicePids = objectDeviceList.Where(e=>e.ObjectCode == objectData.ObjectCode).Select(e=>e.Pid).ToArray()
            };

            postFusionObjectInputs.Add(addObjectInput);
        }

        List<PostAPIResult> addObjectResults = await _objectService.AddObject(postFusionObjectInputs);

        // 取出Fusion新增成功的ObjectData, ObjectDevice, ObjectDeviceDetail
        List<ObjectDatum> successObjectDataList = objectDataList.Where(e => addObjectResults.Any(a => a.code == e.ObjectCode && (a.errors == null || a.errors.Count == 0))).ToList();
        List<Repository.Models.Entities.ObjectDevice> successObjectDeviceList = objectDeviceList.Where(e => successObjectDataList.Any(s => s.ObjectCode == e.ObjectCode)).ToList();
        List<Repository.Models.Entities.ObjectDeviceDetail> successObjectDeviceDetailList = objectDeviceDetailList.Where(e => successObjectDataList.Any(s => s.ObjectCode == e.ObjectCode)).ToList();

        _logService.Logging("info", logActionName, requestUUID, "successObjectDeviceDetailList=>" + JsonSerializer.Serialize(successObjectDeviceDetailList));
        //_logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(errors));

        // 將新增成功的ObjectData, ObjectDevice, ObjectDeviceDetail寫入DB
        await _dataAccessService.CreateRangeAsync(successObjectDataList);
        await _dataAccessService.CreateRangeAsync(successObjectDeviceList);
        await _dataAccessService.CreateRangeAsync(successObjectDeviceDetailList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> addObjectErrors = addObjectResults
            .AsParallel()
            .Select((result, i) => new
            {
                Index = i,
                Result = result
            })
            .Where(x => x.Result.errors?.Any() == true)
            .Select(x => new ReturnError
            {
                index = x.Index,
                code = x.Result.code,
                errors = x.Result.errors
                    .Select((error, errorIndex) => new ErrorDetail
                    {
                        index = errorIndex,
                        error = Constants.ErrorCode.FusionError,
                        code = null,
                        message = null,
                        innerMsg = error.error,
                        details = []
                    })
                    .ToList()
            })
            .ToList();

        // 若有錯誤，記錄Log並回傳且不繼續執行（綁定事件）
        if (addObjectErrors.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(addObjectErrors));
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = addObjectErrors
            };

            _logService.Logging("info", logActionName, requestUUID, "End");

            return StatusCode(returnModel.httpStatus, returnModel);
        }

        List<PostFusionEventInput> postFusionEventInputs = [];
        List<PatchFusionEventInput> patchFusionEventInputs = [];

        // 新增ObjectEvent
        foreach (var objectEvent in objectEventList)
        {
            // 若EventCode已存在，則刪除原有資料
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.ObjectEvent>(e => e.AppCode == appCode && e.AreaCode == objectEvent.AreaCode && e.ObjectCode == objectEvent.ObjectCode && e.Pid == objectEvent.Pid && e.ServiceCode == objectEvent.ServiceCode && e.SddResource == objectEvent.SddResource && e.SddComp == objectEvent.SddComp);

            // 若ServiceCode為Enter or Leave，則刪除EventFence
            if (new[] { "Enter", "Leave" }.Contains(objectEvent.ServiceCode))
            {
                await _dataAccessService.DeleteAsync<EventFence>(e=>e.EventCode == objectEvent.EventCode);
            }

            PostFusionEventInput postFusionEventInput = new ()
            {
                enable = true,
                code = objectEvent.EventCode,
                name = _configurationService.GenerateEventName(new Models.Service.Configuration.InGenerateEventName 
                { 
                    ServiceCode = objectEvent.ServiceCode,
                    FenceName = fenceList.FirstOrDefault(e=>e.FenceCode== _configurationService.GetFenceCodeByEventCode(objectEvent.EventCode))?.FenceName??"",
                    EventCode = objectEvent.EventCode,
                    EventName = objectEvent.EventName
                }),
                serviceCode = objectEvent.ServiceCode,
                sponsorDevice = new PostFusionEventInput.SponsorDevice
                {
                    devicePids = [objectEvent.Pid]
                }
            };
            PatchFusionEventInput patchFusionEventInput = new ()
            {
                enable = true,
                code = objectEvent.EventCode,
                name = _configurationService.GenerateEventName(new Models.Service.Configuration.InGenerateEventName 
                { 
                    ServiceCode = objectEvent.ServiceCode,
                    FenceName = fenceList.FirstOrDefault(e=>e.FenceCode== _configurationService.GetFenceCodeByEventCode(objectEvent.EventCode))?.FenceName??"",
                    EventCode = objectEvent.EventCode,
                    EventName = objectEvent.EventName
                }),
                serviceCode = objectEvent.ServiceCode,
                sponsorDevice = new PatchFusionEventInput.SponsorDevice
                {
                    devicePids = [objectEvent.Pid]
                }
            };

            if (objectEvent.ServiceCode == "SensorDataDriven")
            {
                var objectDeviceDetail = objectDeviceDetailList.First(
                    e => e.AppCode == appCode && 
                    e.AreaCode == objectEvent.AreaCode && 
                    e.ObjectCode == objectEvent.ObjectCode && 
                    e.Pid == objectEvent.Pid && 
                    e.SddResource == objectEvent.SddResource && 
                    e.SddComp == objectEvent.SddComp);

                postFusionEventInput.arguments =
                [
                    new (){key = "sddComp", value = objectEvent.SddComp},
                    new (){key = "sddValue", value = objectDeviceDetail.Threshold.ToString()},
                    new (){key = "autoTreated", value = new[] { "/1043/0/23", "/1043/0/28", "/1043/0/29" }.Contains(objectEvent.SddResource) ? "false" : "true"}, // 如果是起身提醒 @SNS@GU "/1043/0/28", 無體動 @SNS@MV "/1043/0/23", 離床預警 @SNS@LW "/1043/0/29" 的話 autoTreated 為 false
                    new (){key = "sddResource", value = objectEvent.SddResource},
                    new (){key = "sddDuration", value = objectDeviceDetail.Duration.ToString()},
                    new (){key = "alarmInterval", value = objectDeviceDetail.SilentInterval.ToString()},
                    new (){key = "eventRecreatable", value = new[] { "/1043/0/23", "/1043/0/28", "/1043/0/29" }.Contains(objectEvent.SddResource) ? "1" : "0"}, // 如果是起身提醒 @SNS@GU "/1043/0/28", 無體動 @SNS@MV "/1043/0/23", 離床預警 @SNS@LW "/1043/0/29" 的話增加 eventRecreatable 為 1 否則為 0
                ];
                patchFusionEventInput.arguments = postFusionEventInput.arguments.Select(arg => new PatchFusionEventInput.Argument
                {
                    key = arg.key,
                    value = arg.value
                }).ToList();
            }
            else if (new[] { "Enter", "Leave" }.Contains(objectEvent.ServiceCode))
            {
                string fenceCode = _configurationService.GetFenceCodeByEventCode(objectEvent.EventCode);

                var fence = fenceList.First(e => e.AppCode == appCode && e.AreaCode == objectEvent.AreaCode && e.FenceCode == fenceCode);

                postFusionEventInput.stationLayers = fenceStationList.Where(e => e.AreaCode == objectEvent.AreaCode && e.FenceCode == fenceCode)
                    .Select(f => new PostFusionEventInput.StationLayer
                    {
                        sid = f.Stationsid,
                        layer = int.Parse(f.Layer)
                    }).ToList();
                patchFusionEventInput.stationLayers = postFusionEventInput.stationLayers
                    .Select(sl => new PatchFusionEventInput.StationLayer
                    {
                        sid = sl.sid,
                        layer = sl.layer
                    }).ToList();

                postFusionEventInput.stationAlertGroups = new PostFusionEventInput.StationAlertGroups
                {
                    stationSids = fenceAlarmGroupList.Where(e=>e.FenceCode == fenceCode).Select(e=>e.Stationsid).ToList(),
                };
                patchFusionEventInput.stationAlertGroups = new PatchFusionEventInput.StationAlertGroups
                {
                    stationSids = fenceAlarmGroupList.Where(e => e.FenceCode == fenceCode).Select(e => e.Stationsid).ToList(),
                };

                postFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "noSignalAsLeave", value = "true"},
                    new (){key = "rssiDelta1", value = fence.RSSIDelta1.ToString()},
                    new (){key = "rssiDelta2", value = fence.RSSIDelta2.ToString()},
                ];
                patchFusionEventInput.arguments = postFusionEventInput.arguments.Select(arg => new PatchFusionEventInput.Argument
                {
                    key = arg.key,
                    value = arg.value
                }).ToList();
            } else if (new[] { "mmWaveFallDetection", "mmWaveLeaveBed", "mmWaveStayTimeout" }.Contains(objectEvent.ServiceCode))
            {
                var objectDeviceDetail = objectDeviceDetailList.FirstOrDefault(
                    e => e.AppCode == appCode && 
                    e.AreaCode == objectEvent.AreaCode && 
                    e.ObjectCode == objectEvent.ObjectCode && 
                    e.Pid == objectEvent.Pid && 
                    e.SddResource == objectEvent.SddResource);

                // mmWaveFallDetection 只需要 autoTreated 的參數：如果是 mmWaveFallDetection 事件時 autoTreated 為 false；其餘為 true
                postFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = objectEvent.ServiceCode == "mmWaveFallDetection" ? "false" : "true"},
                ];

                // 如果是 mmWaveLeaveBed 事件不需要增加 sddDuration
                // 如果是 mmWaveStayTimeout, mmWaveLeaveBed 事件增加 threshold
                if (objectEvent.ServiceCode == "mmWaveStayTimeout" || objectEvent.ServiceCode == "mmWaveLeaveBed")
                {
                    postFusionEventInput.arguments.Add(new (){key = "threshold", value = objectDeviceDetail?.Threshold?.ToString()??""});
                }
                patchFusionEventInput.arguments = postFusionEventInput.arguments.Select(arg => new PatchFusionEventInput.Argument
                {
                    key = arg.key,
                    value = arg.value
                }).ToList();
            }

            postFusionEventInputs.Add(postFusionEventInput);
            patchFusionEventInputs.Add(patchFusionEventInput);
        }

        List<PostAPIResult> postEventResults = new List<PostAPIResult>();

        // 執行新增 Fusion Even: 先執行更新啟用，如果 event 已經存在則啟用成功，否則執行新增
        var  enablePatchFusionEventInputList = patchFusionEventInputs.ToList();
        if (enablePatchFusionEventInputList.Any())
        {
            var patchResults = await _eventService.PatchActiveEvents(enablePatchFusionEventInputList);

            // 把 PatchAPIResult 列表更新成功的轉成 PostAPIResult 列表
            var postResults = patchResults.Where(patchResult => patchResult.errors == null || patchResult.errors.Count == 0) 
            .Select(patchResult => new PostAPIResult
            {
                code = patchResult.code,
                pid = patchResult.pid,
                sid = patchResult.sid,
                id = patchResult.id,
                index = patchResult.index,
                errors = patchResult.errors?.Select(error => new FusionCoreAPIError
                {
                    error = error.error,
                    description = error.description,
                    descriptions = error.descriptions
                }).ToList()
            })
            .ToList();

            postEventResults.AddRange(postResults);

            // 把 PatchAPIResult 列表更新失敗的執行新增
            postFusionEventInputs = postFusionEventInputs.Where(postFusionEventInput => patchResults.Any(p => p.code == postFusionEventInput.code && (p.errors != null && p.errors.Count > 0))).ToList();
        }

        var addPostFusionEventInputList = postFusionEventInputs.ToList();
        if (addPostFusionEventInputList.Any())
        {
           var postResults = await _eventService.AddActiveEvent(addPostFusionEventInputList);
           postEventResults.AddRange(postResults);
        }

        // 取出Fusion新增成功的ObjectEvent, EventFence
        List<Repository.Models.Entities.ObjectEvent> successObjectEventList = objectEventList.Where(e => postEventResults.Any(p => p.code == e.EventCode && (p.errors == null || p.errors.Count == 0))).ToList();
        List<Repository.Models.Entities.EventFence> successEventFenceList = eventFenceList.Where(e => successObjectEventList.Any(p => p.EventCode == e.EventCode)).ToList();
        
        // 將新增成功的ObjectEvent, EventFence寫入DB
        await _dataAccessService.CreateRangeAsync(successObjectEventList);
        await _dataAccessService.CreateRangeAsync(successEventFenceList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> postEventErrors = postEventResults
            .AsParallel()
            .Select((result, i) => new
            {
                Index = i,
                Result = result
            })
            .Where(x => x.Result.errors?.Any() == true)
            .Select(x => new ReturnError
            {
                index = x.Index,
                code = x.Result.code,
                errors = x.Result.errors
                    .Select((error, errorIndex) => new ErrorDetail
                    {
                        index = errorIndex,
                        error = Constants.ErrorCode.FusionError,
                        code = null,
                        message = error.description,
                        innerMsg = error.error,
                        details = []
                    })
                    .ToList()
            })
            .ToList();

        // 若有錯誤，記錄Log並回傳BadRequest
        if (postEventErrors.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(addObjectErrors));
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = postEventErrors
            };

            _logService.Logging("info", logActionName, requestUUID, "End");

            return StatusCode(returnModel.httpStatus, returnModel);
        }

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = StatusCodes.Status201Created,
            result = true,
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("ugcolors")]
    public async Task<IActionResult> UpdateUgColor([FromBody, RequestNotNullOrEmpty(ErrorMessage = Constants.ErrorCode.RequestNullOrEmpty)] InUpdateUgColor param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        // 資料檢核成功，開始修改
        _logService.Logging("info", logActionName, requestUUID, $"{GetType().Name}.{this.ControllerContext.ActionDescriptor.ActionName} param(s) Validated, start update.");

        // 取得要更新的ObjectData
        var willUpdateObject = await _dataAccessService.Fetch<ObjectDatum>(e=>e.AppCode == _user.AppCode && param.ObjectCodes.Contains(e.ObjectCode) ).ToListAsync();

        // 開始交易
        _dataAccessService.BeginTransaction();

        // 更新ObjectData
        if (willUpdateObject != null) {
            foreach (var obj in willUpdateObject)
            {
                obj.UrgColor = param.UrgColor;
                obj.ModifyDate = DateTime.Now;
                obj.ModifyUserAccount = _user.Account;
            }

            await _dataAccessService.UpdateRangeAsync(
                entities: willUpdateObject,
                updateProperties: new Expression<Func<ObjectDatum, object>>[]
                {
                    e => e.UrgColor,
                    e => e.ModifyDate,
                    e => e.ModifyUserAccount
                }
            );
        }

        // 提交交易
        await _dataAccessService.CommitAsync();

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = StatusCodes.Status200OK,
            result = true
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("objects")]
    [RequestParamListDuplicate("ObjectCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateObject([FromBody] List<InUpdateObject> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;

        // 資料檢核成功，開始修改
        _logService.Logging("info", logActionName, requestUUID, "Object Data Validated, start update.");

        // 將三層結構各自拆開成ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent, EventFence
        var (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList, eventFenceList) = _configurationService.ExtractUpdateObjectParam(appCode, paramList);
        
        // 取得此次要修改的ObjectData
        InGetObjectRelationData inGetObjectRelationData = new()
        {
            AppCode = appCode,
            ObjectList = objectDataList.Select(e => new InGetObjectRelationData.ObjectInfo { ObjectCode = e.ObjectCode }).ToList()
        };

        // 取得目前Table中的ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent，以利後續比對決定那些要新增、刪除
        var (sourceObjectDataList, sourceObjectDeviceList, sourceObjectDeviceDetailList, sourceObjectEventList) = await _configurationService.GetObjectRelationData(inGetObjectRelationData);

        foreach(var param in paramList)
        {
            var objectData = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && e.ObjectCode == param.ObjectCode).AsTracking().FirstAsync();

            // 如果 param.ObjectDeviceList 不為 null 則更新 Fusion Object 確保 Object 跟 Device 的綁定
            bool updateFusionResult = true;
            if (param.ObjectDeviceList != null)
            {
                var fusionResult = await _objectService.PatchObject([new() { code = objectData.ObjectCode, name = param.Name, devicePids = objectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode).Select(e => e.Pid).ToArray() }]);
                updateFusionResult = fusionResult.Any(e => e.errors == null || e.errors.Count == 0);
            }

            // 成功更新 Fusion，開始更新對象資料
            if (updateFusionResult)
            {
                
                List<string> updateField = new List<string>();
                if (param.Enable != null)
                {
                    updateField.Add("Enable");
                    objectData.Enable = param.Enable == "Y";
                }

                if (!string.IsNullOrWhiteSpace(param.GroupCode))
                {
                    updateField.Add("GroupCode");
                    objectData.GroupCode = param.GroupCode;
                }

                if (!string.IsNullOrWhiteSpace(param.Name))
                {
                    updateField.Add("Name");
                    objectData.Name = param.Name;
                }

                if (!string.IsNullOrWhiteSpace(param.ObjectType))
                {
                    updateField.Add("ObjectType");
                    objectData.ObjectType = param.ObjectType;
                }

                if (!string.IsNullOrWhiteSpace(param.UsageDepartCode))
                {
                    updateField.Add("UsageDepartCode");
                    objectData.UsageDepartCode = param.UsageDepartCode;
                }

                if (!string.IsNullOrWhiteSpace(param.Remark))
                {
                    updateField.Add("Remark");
                    objectData.Remark = param.Remark;
                }

                if (!string.IsNullOrWhiteSpace(param.EquipmentStatus))
                {
                    updateField.Add("EquipmentStatus");
                    objectData.EquipmentStatus = param.EquipmentStatus;
                }

                if (!string.IsNullOrWhiteSpace(param.Utilization))
                {
                    updateField.Add("Utilization");
                    objectData.Utilization = param.Utilization == "Y";
                }

                if (!string.IsNullOrWhiteSpace(param.UrgColor))
                {
                    updateField.Add("UrgColor");
                    objectData.UrgColor = param.UrgColor;
                }

                // 如果 ObjectDeviceList 不為 null 則更新 ObjectDevice, ObjectDeviceDetail, ObjectEvent 先刪除再新增
                if (param.ObjectDeviceList != null)
                {
                    // 如果 ObjectDeviceList 為空列表就解除綁定：刪除 ObjectDevice, ObjectDeviceDetail, ObjectEvent 以及 EventFence
                    if (param.ObjectDeviceList.Count() == 0)
                    {
                        // 取得要刪除的ObjectDevice, ObjectEvent
                        var deleteObjectDeviceList = sourceObjectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode).ToList();
                        var deleteObjectEventList = sourceObjectEventList.Where(e => e.ObjectCode == objectData.ObjectCode).ToList();

                        await DeleteObjectEventsThenObjectDevices(deleteObjectDeviceList, deleteObjectEventList);
                    }
                    else {
                        // 刪除存在資料庫但不存在當前 ObjectDeviceList 的資料
                        var filteredObjectDevices = sourceObjectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && !param.ObjectDeviceList.Any(od => od.Pid == e.Pid)).ToList();
                        if (filteredObjectDevices.Any())
                        {
                            var deleteObjectEventList = sourceObjectEventList.Where(e => e.ObjectCode == objectData.ObjectCode && filteredObjectDevices.Any(x => x.Pid == e.Pid)).ToList();
                            await DeleteObjectEventsThenObjectDevices(filteredObjectDevices, deleteObjectEventList);
                        }

                        // 先刪除再重新建立 ObjectDeviceList
                        // 取得要刪除的ObjectDevice 資料（Pid 在 ParamList.ObjectDeviceList 裡且該筆 ObjectDevice 的 ObjectDeviceDetailList 不等於 null）
                        var deleteObjectDeviceList = sourceObjectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && param.ObjectDeviceList.Any(od => od.Pid == e.Pid && od.ObjectDeviceDetailList != null)).ToList();

                        // 取得要新增的 ObjectDevice 資料（Pid 在 ParamList.ObjectDeviceList 裡且該筆 ObjectDevice 的 ObjectDeviceDetailList 不等於 null）
                        var addObjectDeviceList = objectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && param.ObjectDeviceList.Any(od => od.Pid == e.Pid && od.ObjectDeviceDetailList != null)).ToList();
                        var addObjectDeviceDetailList = objectDeviceDetailList.Where(e => addObjectDeviceList.Any(o => o.Pid == e.Pid && o.ObjectCode == e.ObjectCode)).ToList();

                        await DeleteThenCreateObjectDevices(deleteObjectDeviceList, addObjectDeviceList, addObjectDeviceDetailList);

                        // 處理每一筆 ObjectDevice
                        foreach (var od in param.ObjectDeviceList)
                        {
                            // 如果 ObjectDevice.ObjectEventList 不為 null 就重新寫入 ObjectEvent
                            if (od.ObjectEventList != null)
                            {
                                // 取得要刪除的 ObjectEvent 資料
                                var deleteObjectEventList = sourceObjectEventList.Where(e => e.ObjectCode == objectData.ObjectCode && e.Pid == od.Pid).ToList();

                                // 取得要新增的 ObjectEvent 資料
                                var addObjectDeviceListOfPid = objectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && e.Pid == od.Pid).ToList();
                                var addObjectDeviceDetailListOfPid = objectDeviceDetailList.Where(e => addObjectDeviceListOfPid.Any(o => o.Pid == e.Pid && o.ObjectCode == e.ObjectCode)).ToList();
                                var addObjectEventList = objectEventList.Where(e => e.ObjectCode == objectData.ObjectCode && e.Pid == od.Pid).ToList();

                                await DeleteThenCreateObjectEvents(deleteObjectEventList, addObjectEventList, addObjectDeviceDetailListOfPid);
                            }
                        }
                    }
                }

                updateField.Add("ModifyDate");
                updateField.Add("ModifyUserAccount");
                objectData.ModifyDate = DateTime.Now;
                objectData.ModifyUserAccount = _user.Account;

                await _dataAccessService.UpdateAsync<ObjectDatum>(objectData, updateField.ToArray());
            }
        }

         _logService.Logging("info", logActionName, requestUUID, "Object Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    private async Task<bool> ResolveTasks(List<int> taskIdList)
    {
        // 解除說明為 Unpair
        string clearMoreDesc = "Unpair";

        // 依序處理每一個 TaskId
        foreach (int taskId in taskIdList)
        {
            // 解除 Fusion 事件
            var fusionResult = await _monitorService.ClearFusionTask(taskId, clearMoreDesc);

            // Fusion 事件解除失敗
            if (!fusionResult.Any(x => x.errors == null))
            {
                return false;
            }

            // 刪除此Task 原有 cannedMessage
            await _dataAccessService.DeleteAsync<EventCannedMessage>(e => e.TaskId == taskId);

            // 取得事件資料
            var taskDataList = await _dataAccessService.Fetch<TaskDatum>(e => e.AppCode == _user.AppCode && e.TaskId == taskId).AsTracking().ToListAsync();
            foreach (var task in taskDataList) 
            {
                task.TaskClearMoreDesc = clearMoreDesc;

                // Action=10 更改為 30 時 FinishesAt 才更新為系統時間
                if (task.Action == 10)
                {
                    task.FinishesAt = DateTime.Now;
                }
                task.Action = 30;
                task.ModifiesAt = DateTime.Now;

                // 更新TaskData
                await _dataAccessService.UpdateAsync(task,
                                                    callMethodName: null,
                                                    e => e.TaskClearMoreDesc,
                                                    e => e.Action,
                                                    e => e.FinishesAt,
                                                    e => e.ModifiesAt);

            }
        }
        return true;
    }

    private async Task DeleteObjectEventsThenObjectDevices(List<ObjectDevice> deleteObjectDeviceList, List<ObjectEvent> deleteObjectEventList)
    {
        // 自動解除發生中的事件
        bool resolveTaskResult = true;
        var nonResolveTaskList = await _monitorService.GetFollowingTaskWithoutPerm(deleteObjectEventList.Select(e => e.EventCode).ToList());
        if (nonResolveTaskList.Any())
        {
            List<int> taskIdList = nonResolveTaskList
                .Where(task => task.TaskId.HasValue) // 過濾掉 null 值
                .Select(task => task.TaskId.Value)  // 取出非 null 值
                .ToList();
            resolveTaskResult |= ResolveTasks(taskIdList).Result;
        }

        // 如果解除事件失敗就返回不繼續執行後續
        if (!resolveTaskResult)
        {
            return;
        }

        // 刪除ObjectEvent 以及 EventDence 資料
        OutDeleteEvents outDeleteEvents = await _configurationService.DeleteEvents(deleteObjectEventList);

        // 判斷刪除 ObjectEvent 是否成功，成功的話才繼續執行刪除 ObjectDevice 以及 ObjectDeviceTail
        bool deleteEventsResult = deleteObjectEventList.All(oe =>
        {
            // 檢查 EventCode 是否存在於 DeleteTableResultDict 的鍵中
            if (outDeleteEvents.DeleteTableResultDict.TryGetValue(oe.EventCode, out var dbResultList))
            {
                // 檢查 dbResultList 是否包含符合條件的 DBResult
                return dbResultList.Any(dbResult => dbResult.code == oe.EventCode && dbResult.result);
            }

            // 如果鍵不存在，則視為條件不成立
            return false;
        });

        // 刪除ObjectDevice, ObjectDeviceDetail 資料
        if (deleteEventsResult)
        {
            await _configurationService.UboundObjectDevice(deleteObjectDeviceList);
        }
    }

    private async Task DeleteThenCreateObjectDevices(List<ObjectDevice> deleteObjectDeviceList, List<ObjectDevice> addObjectDeviceList, List<Web.Repository.Models.Entities.ObjectDeviceDetail> addObjectDeviceDetailList)
    {
        // 刪除ObjectDevice, ObjectDeviceDetail 資料
        OutDeleteDevices outDeleteDevices = await _configurationService.UboundObjectDevice(deleteObjectDeviceList);

        // 判斷刪除 ObjectDevice 是否成功，成功的話才執行新增 ObjectDevice 以及 ObjectDeviceTail
        bool deleteDevicesResult = deleteObjectDeviceList.All(od =>
        {
            // 檢查 Pid 是否存在於 DeleteTableResultDict 的鍵中
            if (outDeleteDevices.DeleteTableResultDict.TryGetValue(od.Pid, out var dbResultList))
            {
                // 檢查 dbResultList 是否包含符合條件的 DBResult
                return dbResultList.Any(dbResult => dbResult.code == od.Pid && dbResult.result);
            }

            // 如果鍵不存在，則視為條件不成立
            return false;
        });

        // 新增 ObjectDevice, ObjectDeviceDetail 資料
        if (deleteDevicesResult)
        {
            // 新增ObjectDevice 資料（即綁定新裝置）
            var OutCreateObjectDeviceResult = await _configurationService.CreateObjectDevice(_user.AppCode, 
                addObjectDeviceList
            );

            // 將幫定裝置成功的 ObjectDeviceDetail 資料寫入 DB
            var successObjectDeviceDetailList = addObjectDeviceDetailList.Where(odd => OutCreateObjectDeviceResult.InsertObjectDeviceResultList.Any(odResult => odResult.code == odd.ObjectCode && odResult.result)).ToList();
            await _configurationService.CreateObjectDeviceDetail(successObjectDeviceDetailList);
        }
    }

    private async Task DeleteThenCreateObjectEvents(List<ObjectEvent> deleteObjectEventList, List<ObjectEvent> addObjectEventList, List<Web.Repository.Models.Entities.ObjectDeviceDetail> addObjectDeviceDetailList)
    {
        // 自動解除發生中的事件
        bool resolveTaskResult = true;
        var nonResolveTaskList = await _monitorService.GetFollowingTaskWithoutPerm(deleteObjectEventList.Where(e => !addObjectEventList.Any(ae => ae.EventCode == e.EventCode)).Select(e => e.EventCode).ToList());
        if (nonResolveTaskList.Any())
        {
            List<int> taskIdList = nonResolveTaskList
                .Where(task => task.TaskId.HasValue) // 過濾掉 null 值
                .Select(task => task.TaskId.Value)  // 取出非 null 值
                .ToList();
            resolveTaskResult |= ResolveTasks(taskIdList).Result;
        }

        // 如果解除事件失敗就返回不繼續執行後續
        if (!resolveTaskResult)
        {
            return;
        }

        // 刪除ObjectEvent 以及 EventDence 資料
        OutDeleteEvents outDeleteEvents = await _configurationService.DeleteEvents(deleteObjectEventList);

        // 判斷刪除 ObjectEvent 是否成功，成功的話才繼續執行刪除 ObjectDevice 以及 ObjectDeviceTail
        bool deleteEventsResult = deleteObjectEventList.All(oe =>
        {
            // 檢查 EventCode 是否存在於 DeleteTableResultDict 的鍵中
            if (outDeleteEvents.DeleteTableResultDict.TryGetValue(oe.EventCode, out var dbResultList))
            {
                // 檢查 dbResultList 是否包含符合條件的 DBResult
                return dbResultList.Any(dbResult => dbResult.code == oe.EventCode && dbResult.result);
            }

            // 如果鍵不存在，則視為條件不成立
            return false;
        });

        // 新增 ObjectEvent, EventFence 資料
        if (deleteEventsResult)
        {
            // 新增 ObjectEvent 及 Fusion Event 資料，這裡傳入ObjectDeviceDetailList是因為SensorDataDriven事件需要傳入Argument給Console
            if (addObjectEventList != null && addObjectEventList.Count() > 0)
                await _configurationService.CreateEvents(_user.AppCode, addObjectEventList, addObjectDeviceDetailList);
        }
    }

    private async Task<(List<InUpdateObject>, List<Repository.Models.Entities.ObjectDevice>, List<Repository.Models.Entities.ObjectDeviceDetail>, List<Repository.Models.Entities.ObjectEvent>)> GetInUpdateObjectListByChangeObjectDeviceFrom(List<InChangeObjectDevice> paramList)
    {
        // 取得目前Table中的ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent，以利後續比對決定那些要新增、刪除
        var (sourceObjectDataList, sourceObjectDeviceList, sourceObjectDeviceDetailList, sourceObjectEventList) = await _configurationService.GetObjectRelationData(new InGetObjectRelationData()
        {
            AppCode = _user.AppCode,
            ObjectList = paramList.Select(e => new InGetObjectRelationData.ObjectInfo { ObjectCode = e.FromObjectCode }).ToList()
        });

        // 將InChangeObjectDevice轉換成InUpdateObject
        var inUpdateObjectList = paramList.Select(e => new InUpdateObject
        {
            AreaCode = e.AreaCode,
            ObjectCode = e.FromObjectCode,
            Name = sourceObjectDataList.FirstOrDefault(od => od.ObjectCode == e.FromObjectCode)?.Name,
            ObjectDeviceList = sourceObjectDeviceList.Where(od => od.ObjectCode == e.FromObjectCode && !paramList.Any(p => p.Pid == e.Pid)).Select(od => new InUpdateObjectDevice
            {
                AreaCode = od.AreaCode,
                Pid = od.Pid,
            }).ToList()
        }).ToList();

        // 暫存要綁定鋅對象的 ObjectDeviceList
        sourceObjectDeviceList = sourceObjectDeviceList.Where(e => paramList.Any(p => p.Pid == e.Pid)).ToList();

        // 暫存要綁定鋅對象的 ObjectDeviceDetailList
        sourceObjectDeviceDetailList = sourceObjectDeviceDetailList.Where(e => sourceObjectDeviceList.Any(p => p.Pid == e.Pid)).ToList();

        // 暫存要綁定鋅對象的 ObjectEventList
        sourceObjectEventList = sourceObjectEventList.Where(e => sourceObjectDeviceList.Any(p => p.Pid == e.Pid)).ToList();

        return (inUpdateObjectList, sourceObjectDeviceList, sourceObjectDeviceDetailList, sourceObjectEventList);
    }

    private async Task<List<InUpdateObject>> GetInUpdateObjectListByChangeObjectDeviceTo(List<InChangeObjectDevice> paramList, List<Repository.Models.Entities.ObjectDevice> addObjectDeviceList, List<Repository.Models.Entities.ObjectDeviceDetail> addObjectDeviceDetailList, List<Repository.Models.Entities.ObjectEvent> addObjectEventList)
    {
        // 將要新增的 ObjectDevice, ObjectDeviceDetail, ObjectEvent 的 ObjectCode 替換成 paramList 的 ToObjectCode
        addObjectDeviceList.ForEach(e => e.ObjectCode = paramList.First(p => p.Pid == e.Pid).ToObjectCode);
        addObjectDeviceDetailList.ForEach(e => e.ObjectCode = paramList.First(p => p.Pid == e.Pid).ToObjectCode);
        addObjectEventList.ForEach(e => e.ObjectCode = paramList.First(p => p.Pid == e.Pid).ToObjectCode);

        // 取得目前Table中的ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent，以利後續比對決定那些要新增、刪除
        var (sourceObjectDataList, sourceObjectDeviceList, sourceObjectDeviceDetailList, sourceObjectEventList) = await _configurationService.GetObjectRelationData(new InGetObjectRelationData()
        {
            AppCode = _user.AppCode,
            ObjectList = paramList.Select(e => new InGetObjectRelationData.ObjectInfo { ObjectCode = e.ToObjectCode }).ToList()
        });

        // 將 addObjectDeviceList, addObjectDeviceDetailList, addObjectEventList 加入到 sourceObjectDeviceList, sourceObjectDeviceDetailList, sourceObjectEventList
        sourceObjectDeviceList.AddRange(addObjectDeviceList);
        sourceObjectDeviceDetailList.AddRange(addObjectDeviceDetailList);
        sourceObjectEventList.AddRange(addObjectEventList);

        // 取得 EventCode 與 EventName 的映射
        var eventCodeEventNameMap = await _eventService.GetEventEventCodeEventNameMap(sourceObjectEventList.Select(e => e.EventCode).ToList());

        // 將InChangeObjectDevice轉換成InUpdateObject
        var inUpdateObjectList = paramList.Select(e => new InUpdateObject
        {
            AreaCode = e.AreaCode,
            ObjectCode = e.ToObjectCode,
            Name = sourceObjectDataList.FirstOrDefault(od => od.ObjectCode == e.ToObjectCode)?.Name,
            ObjectDeviceList = sourceObjectDeviceList.Where(od => od.ObjectCode == e.ToObjectCode).Select(od => new InUpdateObjectDevice
            {
                AreaCode = od.AreaCode,
                Pid = od.Pid,
                ObjectEventList = sourceObjectEventList.Where(oe => oe.ObjectCode == e.ToObjectCode && oe.Pid == od.Pid).Select(oe => new InUpdateObjectEvent
                {
                    ServiceCode = oe.ServiceCode,
                    SddResource = oe.SddResource,
                    SddComp = oe.SddComp,
                    FenceCode = _configurationService.GetFenceCodeByEventCode(oe.EventCode),
                    EventName = eventCodeEventNameMap.GetValueOrDefault(oe.EventCode)
                }).ToList(),
                ObjectDeviceDetailList = sourceObjectDeviceDetailList.Where(odd => odd.ObjectCode == e.ToObjectCode && odd.Pid == od.Pid).Select(odd => new Web.Models.Controller.Object.ObjectDeviceDetail
                {
                    SddResource = odd.SddResource,
                    SddComp = odd.SddComp,
                    Threshold = odd.Threshold ?? 0,
                    StayOvertime = odd.StayOvertime,
                    Duration = odd.Duration,
                    SilentInterval = odd.SilentInterval
                }).ToList(),
                MmWaveType = od.MmWaveType != null ? (od.MmWaveType.Value == true ? "Y" : "N") : null
            }).ToList()
        }).ToList();

        return inUpdateObjectList;
    }

    private async Task<List<bool>> UpdateObjectDevice(string logActionName, string? requestUUID, string appCode, List<InUpdateObject> paramList)
    {
        _logService.Logging("info", logActionName, requestUUID, "Start to remove device from object.");

        // 將三層結構各自拆開成ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent, EventFence
        var (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList, eventFenceList) = _configurationService.ExtractUpdateObjectParam(appCode, paramList);

        // 取得此次要修改的ObjectData
        InGetObjectRelationData inGetObjectRelationData = new()
        {
            AppCode = appCode,
            ObjectList = paramList.Select(e => new InGetObjectRelationData.ObjectInfo { ObjectCode = e.ObjectCode }).ToList()
        };

        // 取得目前Table中的ObjectData, ObjectDevice, ObjectDeviceDetail, ObjectEvent，以利後續比對決定那些要新增、刪除
        var (sourceObjectDataList, sourceObjectDeviceList, sourceObjectDeviceDetailList, sourceObjectEventList) = await _configurationService.GetObjectRelationData(inGetObjectRelationData);

        List<bool> result = new List<bool>();

        foreach (var param in paramList)
        {
            var objectData = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && e.ObjectCode == param.ObjectCode).AsTracking().FirstAsync();

            // 如果 param.ObjectDeviceList 不為 null 則更新 Fusion Object 確保 Object 跟 Device 的綁定
            bool updateFusionResult = true;
            if (param.ObjectDeviceList != null)
            { 
                var fusionResult = await _objectService.PatchObject([new() { code = objectData.ObjectCode, name = param.Name, devicePids = objectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode).Select(e => e.Pid).ToArray() }]);
                updateFusionResult = fusionResult.Any(e => e.errors == null || e.errors.Count == 0);
            }

            // 成功更新 Fusion，開始更新對象資料
            if (updateFusionResult)
            {
                // 如果 ObjectDeviceList 不為 null 則更新 ObjectDevice, ObjectDeviceDetail, ObjectEvent 先刪除再新增
                if (param.ObjectDeviceList != null)
                {
                    // 如果 ObjectDeviceList 為空列表就解除綁定：刪除 ObjectDevice, ObjectDeviceDetail, ObjectEvent 以及 EventFence
                    if (param.ObjectDeviceList.Count() == 0)
                    {
                        // 取得要刪除的ObjectDevice, ObjectEvent
                        var deleteObjectDeviceList = sourceObjectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode).ToList();
                        var deleteObjectEventList = sourceObjectEventList.Where(e => e.ObjectCode == objectData.ObjectCode).ToList();

                        await DeleteObjectEventsThenObjectDevices(deleteObjectDeviceList, deleteObjectEventList);
                    }
                    else
                    {
                        // 刪除存在資料庫但不存在當前 ObjectDeviceList 的資料
                        var filteredObjectDevices = sourceObjectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && !param.ObjectDeviceList.Any(od => od.Pid == e.Pid)).ToList();
                        if (filteredObjectDevices.Any())
                        {
                            var deleteObjectEventList = sourceObjectEventList.Where(e => e.ObjectCode == objectData.ObjectCode && filteredObjectDevices.Any(x => x.Pid == e.Pid)).ToList();
                            await DeleteObjectEventsThenObjectDevices(filteredObjectDevices, deleteObjectEventList);
                        }

                        // 先刪除再重新建立 ObjectDeviceList
                        // 取得要刪除的ObjectDevice 資料（Pid 在 ParamList.ObjectDeviceList 裡且該筆 ObjectDevice 的 ObjectDeviceDetailList 不等於 null）
                        var deleteObjectDeviceList = sourceObjectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && param.ObjectDeviceList.Any(od => od.Pid == e.Pid && od.ObjectDeviceDetailList != null)).ToList();

                        // 取得要新增的 ObjectDevice 資料（Pid 在 ParamList.ObjectDeviceList 裡且該筆 ObjectDevice 的 ObjectDeviceDetailList 不等於 null）
                        var addObjectDeviceList = objectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && param.ObjectDeviceList.Any(od => od.Pid == e.Pid && od.ObjectDeviceDetailList != null)).ToList();
                        var addObjectDeviceDetailList = objectDeviceDetailList.Where(e => addObjectDeviceList.Any(o => o.Pid == e.Pid && o.ObjectCode == e.ObjectCode)).ToList();

                        await DeleteThenCreateObjectDevices(deleteObjectDeviceList, addObjectDeviceList, addObjectDeviceDetailList);

                        // 處理每一筆 ObjectDevice
                        foreach (var od in param.ObjectDeviceList)
                        {
                            // 如果 ObjectDevice.ObjectEventList 不為 null 就重新寫入 ObjectEvent
                            if (od.ObjectEventList != null)
                            {
                                // 取得要刪除的 ObjectEvent 資料
                                var deleteObjectEventList = sourceObjectEventList.Where(e => e.ObjectCode == objectData.ObjectCode && e.Pid == od.Pid).ToList();

                                // 取得要新增的 ObjectEvent 資料
                                var addObjectDeviceListOfPid = objectDeviceList.Where(e => e.ObjectCode == objectData.ObjectCode && e.Pid == od.Pid).ToList();
                                var addObjectDeviceDetailListOfPid = objectDeviceDetailList.Where(e => addObjectDeviceListOfPid.Any(o => o.Pid == e.Pid && o.ObjectCode == e.ObjectCode)).ToList();
                                var addObjectEventList = objectEventList.Where(e => e.ObjectCode == objectData.ObjectCode && e.Pid == od.Pid).ToList();

                                await DeleteThenCreateObjectEvents(deleteObjectEventList, addObjectEventList, addObjectDeviceDetailListOfPid);
                            }
                        }
                    }
                }

                result.Add(true);
            }
            else
            {
                result.Add(false);
            }
        }
        return result;
    }


    [HttpPatch("device/change")]
    [RequestParamListDuplicate("Pid")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> ChangeObjectDevice([FromBody] List<InChangeObjectDevice> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;


        // 資料檢核成功，開始修改
        _logService.Logging("info", logActionName, requestUUID, "Object Data Validated, start to change device.");

        // 取得要移除裝置的 InUpdateObject
        var (fromParamList, removeObjectDeviceList, removeObjectDeviceDetailList, removeObjectEventList) = await GetInUpdateObjectListByChangeObjectDeviceFrom(paramList);

        // 從對象移除裝置
        List<bool> fromResult = await UpdateObjectDevice(logActionName, requestUUID, appCode, fromParamList);

        // 將 fromResult 中的 false 轉成 ReturnError
        List<ReturnError> fromErrors = fromResult
            .AsParallel()
            .Select((result, i) => new
            {
                Index = i,
                Result = result
            })
            .Where(x => !x.Result)
            .Select(x => new ReturnError
            {
                index = x.Index,
                code = fromParamList[x.Index].ObjectCode,
                errors = new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        index = 1,
                        error = Constants.ErrorCode.FusionError,
                        code = null,
                        message = null,
                        innerMsg = Constants.ErrorCode.FusionError,
                        details = []
                    }
                }
            })
            .ToList();

        // 若有錯誤，記錄Log並回傳
        if (fromErrors.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(fromErrors));
            ReturnModel returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = fromErrors
            };

            _logService.Logging("info", logActionName, requestUUID, "End");

            return StatusCode(returnModel.httpStatus, returnModel);
        }

        // 取得要新增裝置的 InUpdateObject
        List<InUpdateObject> toParamList = await GetInUpdateObjectListByChangeObjectDeviceTo(paramList, removeObjectDeviceList, removeObjectDeviceDetailList, removeObjectEventList);

        // 新增裝置到對象
        List<bool> toResult = await UpdateObjectDevice(logActionName, requestUUID, appCode, toParamList);

        // 將 toResult 中的 false 轉成 ReturnError
        List<ReturnError> toErrors = toResult
            .AsParallel()
            .Select((result, i) => new
            {
                Index = i,
                Result = result
            })
            .Where(x => !x.Result)
            .Select(x => new ReturnError
            {
                index = x.Index,
                code = toParamList[x.Index].ObjectCode,
                errors = new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        index = 1,
                        error = Constants.ErrorCode.FusionError,
                        code = null,
                        message = null,
                        innerMsg = Constants.ErrorCode.FusionError,
                        details = []
                    }
                }
            })
            .ToList();
        
        // 若有錯誤，記錄Log並回傳
        if (toErrors.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(toErrors));
            ReturnModel returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = toErrors
            };

            _logService.Logging("info", logActionName, requestUUID, "End");

            return StatusCode(returnModel.httpStatus, returnModel);
        }

         _logService.Logging("info", logActionName, requestUUID, "Object Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 刪除對象
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpDelete("objects")]
    [RequestParamListDuplicate("ObjectCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteObject([FromBody] List<InDeleteObject> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除對象資料
        _logService.Logging("info", logActionName, requestUUID, "Object Data Validated, start to delete.");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        // 取得此次要刪除的ObjectData
        InGetObjectRelationData inGetObjectRelationData = new()
        {
            AppCode = appCode,
            ObjectList = paramList.Select(e => new InGetObjectRelationData.ObjectInfo { ObjectCode = e.ObjectCode }).ToList()
        };

        // 取得目前 Table 中的 ObjectData
        var objectDataByAppCode = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == inGetObjectRelationData.AppCode).ToListAsync();
        var objectDataList = objectDataByAppCode.Where(e => inGetObjectRelationData.ObjectList.Any(o => o.ObjectCode == e.ObjectCode)).ToList();

        // 刪除 ObjectData 資料
        List<DeleteAPIResult> deleteObjectResults = await _configurationService.DeleteObjectDatum(objectDataList);

        // 判斷每一筆的DeleteAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> deleteObjectErrors = deleteObjectResults
            .AsParallel()
            .Select((result, i) => new
            {
                Index = i,
                Result = result
            })
            .Where(x => x.Result.errors?.Any() == true)
            .Select(x => new ReturnError
            {
                index = x.Index,
                code = x.Result.code,
                errors = x.Result.errors
                    .Select((error, errorIndex) => new ErrorDetail
                    {
                        index = errorIndex,
                        error = Constants.ErrorCode.FusionError,
                        code = null,
                        message = null,
                        innerMsg = error.error,
                        details = []
                    })
                    .ToList()
            })
            .ToList();

        // 若有錯誤，記錄Log並回傳Status400BadRequest
        if (deleteObjectErrors.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(deleteObjectErrors));
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = deleteObjectErrors
            };

            _logService.Logging("info", logActionName, requestUUID, "End");

            return StatusCode(returnModel.httpStatus, returnModel);
        }

        _logService.Logging("info", logActionName, requestUUID, "Object Data delete done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 查詢對象
    /// </summary>
    /// <param name="param"></param>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6225
    /// 列表畫面欄位及需求 
    /// 查詢視窗欄位及需求 
    /// </spec>
    /// <returns></returns>
    [HttpGet("objects")]
    public async Task<IActionResult> RetrieveObject([FromQuery] InRetrieveObject queryParam)
    {
        InRetrieveObject param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var objects = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.Active);
        var objectDevices = _dataAccessService.Fetch<ObjectDevice>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode);
        var objectDeviceDetails = _dataAccessService.Fetch<Web.Repository.Models.Entities.ObjectDeviceDetail>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode);
        var objectEvents = _dataAccessService.Fetch<ObjectEvent>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.Active);
        var eventFences = _dataAccessService.Fetch<EventFence>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.Active == true);
        var Fences = _dataAccessService.Fetch<Fence>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode);
        var devices = _dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.Active == true);
        var objectGroups = _dataAccessService.Fetch<ObjectGroup>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode);

        // 從 Fusion 取回 device 列表資料
        var fusionDeviceList = await _deviceService.GetDeviceList().ConfigureAwait(false);

        // 取得系統參數
        var sysParameterList = await _preferenceService.FetchSysParameter(_user.AppCode);

        // 取得TempHudDeviceType的Json 字串
        var tempHudDeviceTypeStr = sysParameterList.FirstOrDefault(e => e.ParaCode == "TempHudDeviceType")?.ParaValue;

        // 將TempHudDeviceType的Json 字串轉成List<string>
        var tempHudDeviceTypeList = JsonSerializer.Deserialize<List<string>>(tempHudDeviceTypeStr);

        // 依指定的PID列表，取得Console DeviceList
        List<Devices> coreDeviceList = await _configurationService.GetCoreDeviceList(string.Join(",", objectDevices.Select(e=>e.Pid)));

        // 處理查詢條件
        var query = from o in objects
                    join og in objectGroups on o.GroupCode equals og.GroupCode into oGroup
                    from og in oGroup.DefaultIfEmpty()
                     select (new
                     {
                         o.AreaCode,
                         o.ObjectCode,
                         o.Name,
                         o.ObjectType,
                         o.UsageDepartCode,
                         Enable = (o.Enable == true) ? "Y" : "N",
                         Active = (o.Active == true) ? "Y" : "N",
                         o.Remark,
                         o.UrgColor,
                         o.GroupCode,
                         og.GroupName,
                         o.EquipmentStatus,
                         Utilization = o.Utilization == true ? "Y" : "N",
                         o.ModifyDate,
                         ObjectDeviceList = (from od in objectDevices
                                            join dev in devices on od.Pid equals dev.Pid into odGroup
                                            from od2 in odGroup.DefaultIfEmpty()
                                            where od.ObjectCode == o.ObjectCode
                                            select new ObjectDeviceQueryResult
                                            {
                                                ObjectCode = od.ObjectCode,
                                                Pid = od.Pid,
                                                MmWaveType = od.MmWaveType != null ? (od.MmWaveType.Value == true ? "Y" : "N") : null,
                                                DeviceType = od2.DeviceType,
                                                DeviceName = od2.Name,
                                                ObjectEventList = (from oe in objectEvents
                                                                    join ef in eventFences on oe.EventCode equals ef.EventCode into efGroup
                                                                    from ef in efGroup.DefaultIfEmpty()
                                                                    join f in Fences on ef.FenceCode equals f.FenceCode into fGroup
                                                                    from f in fGroup.DefaultIfEmpty()
                                                                    where oe.Active == true && oe.Enable == true && oe.Pid == od.Pid && oe.ObjectCode == od.ObjectCode
                                                                select (new ObjectEventQueryResult
                                                                {
                                                                    ObjectCode = oe.ObjectCode,
                                                                    Pid = oe.Pid,
                                                                    ServiceCode = oe.ServiceCode,
                                                                    SddResource = oe.SddResource,
                                                                    SddComp = oe.SddComp,
                                                                    EventCode = oe.EventCode,
                                                                    FenceCode = ef.FenceCode,
                                                                    CustomFenceCode = f.CustomFenceCode,
                                                                })).ToList(),
                                                ObjectDeviceDetailList = (from odd in objectDeviceDetails
                                                                        where odd.Pid == od.Pid && odd.ObjectCode == od.ObjectCode
                                                                    select new ObjectDeviceDetailQueryResult
                                                                    {
                                                                        AreaCode = odd.AreaCode,
                                                                        ObjectCode = odd.ObjectCode,
                                                                        Pid = odd.Pid,
                                                                        SddResource = odd.SddResource,
                                                                        Threshold = odd.Threshold,
                                                                        SddComp = odd.SddComp,
                                                                        StayOvertime = odd.StayOvertime,
                                                                        Duration = odd.Duration,
                                                                        SilentInterval = odd.SilentInterval
                                                                    }).ToList()
                                            }).ToList(),
                     });

        // 先在查詢外處理 Split
        var usageDepartCodes = string.IsNullOrEmpty(param.UsageDepartCodes) ? null : param.UsageDepartCodes.Split(",");
        var deviceTypes = string.IsNullOrWhiteSpace(param.DeviceTypes) ? null : param.DeviceTypes.Split(",");

        query = from re in query
                 where (usageDepartCodes == null || usageDepartCodes.Contains(re.UsageDepartCode))
                    && (string.IsNullOrEmpty(param.ObjectCode) || re.ObjectCode.Contains(param.ObjectCode))
                    && (string.IsNullOrEmpty(param.ObjectName) || re.Name.ToUpper().Contains(param.ObjectName.ToUpper()))
                    && (string.IsNullOrEmpty(param.GroupCode) || re.GroupCode.Contains(param.GroupCode))
                    && (string.IsNullOrEmpty(param.GroupName) || re.GroupName.Contains(param.GroupName))
                    && (string.IsNullOrEmpty(param.ObjectType) || re.ObjectType.Contains(param.ObjectType))
                    && (string.IsNullOrEmpty(param.Enable) || re.Enable == param.Enable)
                    && (re.Active == (string.IsNullOrEmpty(param.Active) ? "Y" : param.Active))
                    && (deviceTypes == null || re.ObjectDeviceList.Any(od => deviceTypes.Contains(od.DeviceType)))
                 select re;

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        // 處理 resultList，每筆資料逐步新增屬性
        foreach (var o in recordList)
        {
            // 處理 ObjectDeviceList
            foreach (var od in o.ObjectDeviceList)
            {
                // 根據 Pid 找到對應的 device
                var device = coreDeviceList.FirstOrDefault(cd => cd.pid == od.Pid);

                // 取得 fusion device 的 configurations
                var configs = device?.configurations ?? Enumerable.Empty<ResourceConfiguration>();

                // 新增 Configs
                od.Configs = configs.ToList();

                // 從 fusionDeviceList 找到對應的 fusionDevice 並設定 Battery
                var fusionDevice = fusionDeviceList.FirstOrDefault(fusion => fusion.pid == od.Pid);

                // 新增 Battery
                od.Battery = fusionDevice?.configurations?.FirstOrDefault(e => e.resourceId == Constants.ResourceId.Battery)?.value ?? "";

                // 處理 ObjectDeviceDetailList
                foreach (var odd in od.ObjectDeviceDetailList)
                {
                    // 預設 config 為 null
                    var config = configs?.FirstOrDefault(c => c.resourceId == odd.SddResource);

                    odd.CurrentValue = config != null ? config.value : null;
                    odd.ReportTime = config != null ? config.latestReportTime : null;
                }
            }
        }

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        return Ok(returnModel);
    }
}
