﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Web.Models.Service.Fusion;

public class CommonAPIResult
{
    public string? code { get; set; }
    public string? pid { get; set; }
    public string? sid { get; set; }
    public long? id { get; set; }
    public List<FusionCoreAPIError> errors { get; set; }
}
public class FusionCoreAPIError
{
    public string error { get; set; }
    public string description { get; set; }
    public List<string>? descriptions { get; set; }
}
public class PostAPIResult : CommonAPIResult { public int? index { get; set; } }
public class PatchAPIResult : CommonAPIResult { public int? index { get; set; } }
public class DeleteAPIResult : CommonAPIResult { }
public class PutAPIResult : CommonAPIResult { public string? mapPath { get; set; } }

public class SendMessageResult : PostAPIResult
{
    public int? success { get; set; }
    public int? fail { get; set; }
}

public class MailModel
{
    public string type { get; set; } = "Mail";
    public MailArguments arguments { get; set; }
    public MailContent content { get; set; }
}
public class MailArguments
{
    public string receiverCode { get; set; }
}
public class MailContent
{
    public string subject { get; set; }
    public string message { get; set; }
}

public class LineModel
{
    public string type { get; set; } = "Line";
    public LineArguments arguments { get; set; }
    public LineContent content { get; set; }
}
public class LineArguments
{
    public string accessToken { get; set; }
    public string url { get; set; }
}
public class LineContent
{
    public string to { get; set; }
    public string message { get; set; }
}

public class ENSMessageModel
{
    public string type { get; set; } = "ENS";
    public ENSMessageArguments arguments { get; set; }
    public ENSMessageContent content { get; set; }
}
public class ENSMessageArguments
{
    public string url { get; set; }
}
public class ENSMessageContent
{
    public string objectCode { get; set; }
    public int taskId { get; set; }
    public string taskType { get; set; }
    public bool callout { get; set; } = false;
    public string message { get; set; }
    public extra taskExtra { get; set; }
    public Position position { get; set; }
}
public class Position
{
    public string toSid { get; set; }
}

public class DisplayMessageModel
{
    public DisplayMessageArguments arguments { get; set; }
    public DisplayMessageContent content { get; set; }
}
public class DisplayMessageArguments
{
    public string receiverCode { get; set; }
}
public class DisplayMessageContent
{
    public string objectCode { get; set; }
    public int taskId { get; set; }
    public string taskType { get; set; }
    public bool callout { get; set; } = false;
    public string calloutNumber { get; set; } = "1";
    public string message { get; set; }
    public string message2 { get; set; }
    public extra taskExtra { get; set; }
}

public class ConsoleApiLogParaModel
{
    public bool enabled { get; set; }
    public string logLevel { get; set; }
    public string clientIP { get; set; }
}
public class Object
{
    public int id { get; set; }
    public Boolean enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public string type { get; set; }
}

public class PostFusionObjectInput
{
    public string code { get; set; }
    public string name { get; set; }
    public string type { get; set; }
    public string[] devicePids { get; set; }
}

public class PatchFusionObjectInput
{
    public string code { get; set; }
    public string name { get; set; }
    public string[] devicePids { get; set; }
}

public class PostFusionEventInput
{
    public bool enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public string serviceCode { get; set; }
    public SponsorDevice sponsorDevice { get; set; }
    public List<StationLayer> stationLayers { get; set; }
    public StationAlertGroups stationAlertGroups { get; set; }
    public List<Argument> arguments { get; set; }
    public List<ExclusionPeriod> exclusionPeriod { get; set; }

    public class SponsorDevice
    {
        public List<string> devicePids { get; set; }
        public List<string> deviceTypes { get; set; }
    }

    public class StationLayer
    {
        public string sid { get; set; }
        public int layer { get; set; }
    }

    public class StationAlertGroups
    {
        public List<string> stationSids { get; set; }
    }

    public class Argument
    {
        public string key { get; set; }
        public string value { get; set; }
    }

    public class ExclusionPeriod
    {
        public string frequency { get; set; }
        public string startsAt { get; set; }
        public string finishedAt { get; set; }
        public string value { get; set; }
        public bool enable { get; set; }
    }
}

public class PatchFusionEventInput
{
    public bool enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public string serviceCode { get; set; }
    public SponsorDevice sponsorDevice { get; set; }
    public List<StationLayer> stationLayers { get; set; }
    public StationAlertGroups stationAlertGroups { get; set; }
    public List<Argument> arguments { get; set; }
    public List<ExclusionPeriod> exclusionPeriod { get; set; }

    public class SponsorDevice
    {
        public List<string> devicePids { get; set; }
        public List<string> deviceTypes { get; set; }
    }

    public class StationLayer
    {
        public string sid { get; set; }
        public int layer { get; set; }
    }

    public class StationAlertGroups
    {
        public List<string> stationSids { get; set; }
    }

    public class Argument
    {
        public string key { get; set; }
        public string value { get; set; }
    }

    public class ExclusionPeriod
    {
        public string frequency { get; set; }
        public string startsAt { get; set; }
        public string finishedAt { get; set; }
        public string value { get; set; }
        public bool enable { get; set; }
    }
}

public class ObjectDeviceInput
{
    public string code { get; set; }
    public string[] devicePids { get; set; }
}

public class AddObjectDeviceInput:ObjectDeviceInput
{

}

public class serviceCode
{
    public string code { get; set; }
    public string name { set; get; }
    public string description { set; get; }
    public langs langs { get; set; }
}
public class langs
{
    public string nameId { get; set; }
    public string descId { get; set; }
}

public class supportDataEvent
{
    public string name { get; set; }
    public string serviceCode { get; set; }
    public bool autoTreated { get; set; }
    public List<sddResource> sddResource { get; set; }
}
public class sddResource
{
    public string id { get; set; }
    public string langsId { get; set; }
    public string sddComp { get; set; }
    public double threshold { get; set; }
}
public class DevicePositions
{
    public int id { get; set; }
    public string pid { get; set; }
    public string name { get; set; }
    public Object @object { get; set; }
    public position position { get; set; }
    public DateTime modifiesAt { get; set; }
}
public class DeviceType
{
    public string type { get; set; }
    public string name { get; set; }
    public langs langs { get; set; }
    public bool isOtaSupported { get; set; }
    public bool isPositioningSupported { get; set; }
    public bool isMsgSupported { get; set; }
    public List<supportDataEvent> supportDataEvent { get; set; }
}


public class DeviceResults
{
    [JsonPropertyName("results")]
    public List<DeviceResults.Result> Results { get; set; }

    public class Result
    {
        [JsonPropertyName("pid")]
        public string Pid { get; set; }
        [JsonPropertyName("type")]
        public string Type { get; set; }
        [JsonPropertyName("device")]
        public DeviceResults.Device Device { get; set; }
        [JsonPropertyName("record")]
        public DeviceResults.Record Record { get; set; }
        [JsonPropertyName("object")]
        public DeviceResults.Object Object { get; set; }
    }

    public class Device
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }
        [JsonPropertyName("enable")]
        public bool Enable { get; set; }
        [JsonPropertyName("registered")]
        public bool Registered { get; set; }
        [JsonPropertyName("name")]
        public string Name { get; set; }
    }

    public class Record
    {
        [JsonPropertyName("resourceId")]
        public string ResourceId { get; set; }
        [JsonPropertyName("value")]
        public string Value { get; set; }
        [JsonPropertyName("reportTime")]
        public long ReportTimeTicks { get; set; }

        //[JsonIgnore]
        //public string ReportTime => DateTimeOffset
        //    .FromUnixTimeMilliseconds(ReportTimeTicks)
        //    // 未來這裡應該要讀取時區設定
        //    .AddHours(8)
        //    // 未來這裡應該要讀取日期格式設定
        //    .ToString("yyyy-MM-dd HH:mm:ss");
        //[JsonIgnore]
        //public string ReportTime => DateTimeOffset
        //    .FromUnixTimeMilliseconds(ReportTimeTicks)
        //    .ToLocalTime()
        //    .ToString("yyyy-MM-dd HH:mm:ss");
        [JsonIgnore]
        public long ReportTime => ReportTimeTicks;
    }
    public class Object
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }
        [JsonPropertyName("enable")]
        public bool Enable { get; set; }
        [JsonPropertyName("code")]
        public string Code { get; set; }
        [JsonPropertyName("name")]
        public string Name { get; set; }
        [JsonPropertyName("type")]
        public string Type { get; set; }
    }
}

public class Devices : DeviceType
{
    public int id { get; set; }
    public Boolean active { get; set; }
    public Boolean enable { get; set; }
    public Boolean registered { get; set; }
    public string pid { get; set; }
    public string name { get; set; }
    public station station { get; set; }
    public Boolean hasObject { get; set; }
    public Object @object { get; set; }
    public List<space> spaces { get; set; }
    public List<department> departments { get; set; }
    public List<ResourceConfiguration> configurations { get; set; }
    public version version { get; set; }
    public connection connection { get; set; }
    public metadata metadata { get; set; }
    public DateTime bootstrapTime { get; set; }
    public position position { get; set; }
    public DateTime modifiesAt { get; set; }
    public int? ImportId { get; set; } //20230920 add for 裝置匯入對照用
}
public class metadata
{
    public int battery { get; set; }
}
public class position
{
    public string point { get; set; }
    public float positionX { get; set; }
    public float positionY { get; set; }
    public station station { get; set; }
    public plane plane { get; set; }
    public DateTime latestPositionTime { get; set; }
}

public class TrajectoryOutput
{
    public long id { get; set; }
    public device device { get; set; }
    public Object @object { get; set; }
    public plane plane { get; set; }
    public location location { get; set; }
    public DateTime positionTime { get; set; }
    public float positionX { get; set; }
    public float positionY { get; set; }
    public station toStation { get; set; }
}

public class EntryExitDurationByHourOutput
{
    public long id { get; set; }
    public device device { get; set; }
    public Object @object { get; set; }
    public station station { get; set; }
    public plane plane { get; set; }
    public location location { get; set; }
    public DateTime positionTime { get; set; }
    public float positionX { get; set; }
    public float positionY { get; set; }
    public int entryCount { get; set; }
    public int exitCount { get; set; }
    public double stayDuration { get; set; }
}

public class plane /*2022/4/22發現api回傳多了這個物件,但文件並沒有*/
{
    public int id { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public double? mapWidth { get; set; }

    public double? mapHeight { get; set; }

    public double? positionX { get; set; }

    public double? positionY { get; set; }
    public string? planeMapPath { get; set; }
}
public class location
{
    public int id { get; set; }
    public string code { get; set; }
    public string name { get; set; }
}
public class ResourceConfiguration
{
    public string resourceId { get; set; }
    public string value { get; set; }
    public DateTime? latestReportTime { get; set; }

}
public class version
{
    public string systemVersion { get; set; }
}
public class connection
{
    public string ip { get; set; }
    public Boolean isConnected { get; set; }
    public DateTime latestConnectedTime { get; set; }
}
public class department
{
    public int id { get; set; }
    public Boolean enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }

}
public class space
{
    public int id { get; set; }
    public Boolean enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }

}

public class station
{
    public int id { get; set; }
    public Boolean enable { get; set; }
    public string sid { get; set; }
    public string name { get; set; }
    public string type { get; set; }

}
public class Stations
{
    public int id { get; set; }
    public Boolean active { get; set; }
    public string attribute { get; set; }
    public Boolean enable { get; set; }
    public Boolean registered { get; set; }
    public Boolean compatible { get; set; }
    public string sid { get; set; }
    public string name { get; set; }
    public string type { get; set; }
    public plane plane { get; set; }
    public float positionX { get; set; }
    public float positionY { get; set; }
    public List<ResourceConfiguration> configurations { get; set; }
    public version version { get; set; }
    public connection connection { get; set; }
    public DateTime modifiesAt { get; set; }
    public string positionImg { get; set; }
    public List<device> devices { get; set; }
}

public class ConfigurationsCompatible
{
    public string sid { get; set; }
    public string name { get; set; }
    public string type { get; set; }
    public List<ResourceConfiguration> configurations { get; set; }
}

/**
 * 因為用Task會跟C#的Task衝突，TaskData 會跟HMN的Table名稱衝突，所以改名為FusionTask
 **/
public class FusionTask
{
    public int? id { get; set; }
    public bool active { get; set; }
    public int action { get; set; }
    public string eventCode { get; set; }
    public string serviceCode { get; set; }
    public DateTime startsAt { get; set; }
    public DateTime finishesAt { get; set; }
    public DateTime modifiesAt { get; set; }
    public string eventName { get; set; }
    public List<sponsorObject> sponsorObjects { get; set; }
    public extra extra { get; set; }
}

public class extra
{
    public fenceEnter digitalFenceEnter { get; set; }
    public fenceLeave digitalFenceLeave { get; set; }
    /// <summary>
    /// 感測器數據
    /// </summary>
    public sensorData sensorDataDriven { get; set; }
    public help help { get; set; }
    /// <summary>
    /// 長按M系列屬求救
    /// </summary>
    public longPress longPress { get; set; }
    public List<deviceError> deviceErrors { get; set; }
    /// <summary>
    /// 毫米波離床提醒
    /// </summary>
    public mmWaveLeaveBed mmWaveLeaveBed { get; set; }
    /// <summary>
    /// 毫米波停留逾時警示
    /// </summary>
    public mmWaveStayTimeout mmWaveStayTimeout { get; set; }
    /// <summary>
    /// 毫米波跌倒偵測
    /// </summary>
    public mmWaveFallDetection mmWaveFallDetection { get; set; }
    /// <summary>
    /// 基站異常
    /// </summary>
    public abnormalStation abnormalStation { get; set; }
}

public class sensorData
{
    public string sddComp { get; set; }
    public string resource { get; set; }
    public float sddValue { get; set; }
    public float reportTime { get; set; }
    public float reportValue { get; set; }
    public float sddDuration { get; set; }
}
public class deviceError
{
    public string error { get; set; }
    public string resource { get; set; }
    public string errorTimes { get; set; }
}

public class longPress
{
    public string nearestSid { get; set; }
    public string nearestPlaneCode { get; set; }
}
public class help
{
    public string nearestSid { get; set; }
    public string nearestPlaneCode { get; set; }
}
public class fenceEnter
{
    public string toSid { get; set; }
    public string fromSid { get; set; }
}
public class fenceLeave
{
    public string toSid { get; set; }
    public string fromSid { get; set; }
    public string fromSidPlaneCode { get; set; }
    public string fromSidPlaneName { get; set; }
    public string fromSidLocCode { get; set; }
    public string fromSidLocName { get; set; }
}

public class mmWaveLeaveBed
{
    public string sid { get; set; }
}

public class mmWaveStayTimeout
{
    public string sid { get; set; }
}
public class mmWaveFallDetection
{
    public string sid { get; set; }
}

public class abnormalStation
{
    public string sid { get; set; }
}

public class SensorDataDriven
{
    public string sddComp { get; set; }
    public string resource { get; set; }
    public float sddValue { get; set; }
    public float reportTime { get; set; }
    public float reportValue { get; set; }
    public float sddDuration { get; set; }
}

public class sponsorObject
{
    public long id { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public bool active { get; set; }
    public bool enable { get; set; }
    public string type { get; set; }
    public List<device> devices { get; set; }
}

public class device
{
    public long id { get; set; }
    public string pid { get; set; }
    public string name { get; set; }
    public string type { get; set; }
}

public class FusionNetParam
{
    public string ApiUrl { get; set; }
    public string SecretKey { get; set; }
    public string ApiVersion { get; set; }
    public string SelfCertificateFile { get; set; }
}

public class TrajectoryInput
{
    public string search { get; set; }
    public int size { get; set; }
    public bool inlinecount { get; set; }
    public string sort { get; set; } = "positionTime,desc";
}

public class InGetDeviceReport
{
    public string DeviceType { get; set; }
    public string? ObjectCodes { get; set; }
    public string? Pids { get; set; }
    public string? PidLike { get; set; }
    public string? ObjectNameLike { get; set; }
    public string? ResourceIds { get; set; }
    public long StartedTime { get; set; }
    public long EndedTime { get; set; }
    public string LineType { get; set; }
    public long Interval { get; set; }
}

public class FusionCamera
{
    public class Configuration
    {
        public string ip { get; set; }
        public string? cameraVideoPath { get; set; }
        public string? taskVideoPath { get; set; }
        public string streamUrl { get; set; }
        public string? username { get; set; }
        public string? userAuth { get; set; }
    }
}

public class AddDeviceInput
{
    public string pid { get; set; }
    public string name { get; set; }
    public string type { get; set; }
    public string? stationSid { get; set; }
}

public class UpdateDeviceInput
{
    public string pid { get; set; }
    public string name { get; set; }
    public string enable { get; set; }
}

public class AddCameraInput
{
    public string code { get; set; }
    public string name { get; set; }
    public List<string> stationSids { get; set; }
    public FusionCamera.Configuration configuration { get; set; }
}

public class PatchCameraInput : AddCameraInput
{
    public bool enable { get; set; }
}

public class GetCameraOutput
{
    public int id { get; set; }
    public bool active { get; set; }
    public bool enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public FusionCamera.Configuration configuration { get; set; }
    public Connection connection { get; set; }
    public string modifiesAt { get; set; }

    public class Connection
    {
        public string latestAliveTime { get; set; }
        public bool isAlive { get; set; }
    }
}

public class AddStationInput
{
    public string sid { get; set; }
    public string name { get; set; }
    public string type { get; set; }
    public float? positionX { get; set; }
    public float? positionY { get; set; }
    public string attribute { get; set; }
    public object otherAttributes { get; set; }
    public string planeCode { get; set; }
    public List<string> spaceCodes { get; set; }
    public List<string> departmentCodes { get; set; }
}

public class Command
{
    public string resourceId { get; set; }
    public string? value { get; set; }
}

public class StationCommandInput
{
    public string sid { get; set; }
    public int timeout { get; set; } = 10000;
    public Command command { get; set; }
}

public class PatchStationInput
{
    public string sid { get; set; }
    public string name { get; set; }
    public bool enable { get; set; }
}

public class PatchConfigurationInput
{
    public string sid { get; set; }
    public int timeout { get; set; } = 100000;
    public List<ResourceConfiguration> configurations { get; set; }
}

public class Planes
{
    public int id { get; set; }
    public Boolean active { get; set; }
    public Boolean enable { get; set; }
    public string code { get; set; }
    public string name { get; set; }
    public string mapPath { get; set; }
    public List<position> positions { get; set; }
    public List<Stations> stations { get; set; }
    public DateTime modifiesAt { get; set; }
}

public class PostPlaneInput
{
    public string code { get; set; }
    public string name { get; set; }
    public List<position> positions { get; set; }
}

public class PutPlaneInput
{
    public string code { get; set; }
    public IFormFile Image { get; set; }
}

public class PatchPlaneInput
{
    public string code { get; set; }
    public bool enabled { get; set; }
    public string name { get; set; }
    public List<position> positions { get; set; }
}

public class GetLicenseServiceOutput
{
    public int id { get; set; }
    public bool active { get; set; }
    public string code { get; set; }
    public License license { get; set; }
    public string modifiesAt { get; set; }

    public class License
    {
        public string signsAt { get; set; }
        public string expiresAt { get; set; }
        public bool isValid { get; set; }
    }
}

public class GetLicenseNotifyConfigOutput
{
    public int id { get; set; }
    public bool enable { get; set; }
    public int notifyDaysBefore { get; set; }
    public string notifyTime { get; set; }
    public string notifySubject { get; set; }
    public string notifyEmails { get; set; }
    public string modifiesAt { get; set; }
}

public class GetSystemConfigurationsOutput
{
    public int id { get; set; }
    public bool active { get; set; }
    public string code { get; set; }
    public string value { get; set; }
    public string group { get; set; }
    public int groupOrder { get; set; }
    public Langs langs { get; set; }
    public string modifiesAt { get; set; }
    public bool siEdit { get; set; }

    public class Langs
    {
        public string nameId { get; set; }
    }
}