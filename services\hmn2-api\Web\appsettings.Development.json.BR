{
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
	    "Microsoft.AspNetCore.SignalR": "Warning",
      "Microsoft.AspNetCore.Http.Connections.Internal.Transports.WebSocketsTransport": "Warning",
	    "Microsoft.EntityFrameworkCore.ChangeTracking": "Warning",
      "Microsoft.EntityFrameworkCore.Database": "Warning"
    }
  },
  "Cors": {
    "AllowOrigin": "*"
  },
  "ConnectionStrings": {
    "FusionS3HMNConnection": "server=************;port=8260;database=FusionS3HMN;uid=postgres;pwd=*************;"
  },
  "FusionNetParam": {
    "ApiUrl": "http://************:8080",
    "ApiVersion": "v3",
    "SecretKey": "SXzcVbuTEhFc5kYY7tOGAcBRjOZ63hCL"
  },
  "MQTTParam": {
    "HostIp": "************",
    "HostPort": "8204", //default: if 域名 9002 else 8201 
    "UseTLS": "N", //default: if 域名 Y else N 
    "Enable": "Y", //default: Y
    "Timeout": 5000,
    "UserName": "",
    "Password": ""
  },
  "MQTTServerParam": {
    "HostPort": "8112",
	"ConnectionBacklog": 100,
	"MaxPendingMessagesPerClient": 1000
  },
  "UtilizationRateScheduleJob": {
    "Enable": "N",
    "jobName": "UtilizationRateScheduleJob",
    "CronExpression": "0 5 * * *" // 每天 05:00 執行
  },
  "ScheduleTaskBackgroundServiceJob": {
    "Enable": "N",
    "jobName": "ScheduleTaskBackgroundServiceJob",
    "CronExpression": "0 0 * * * *" // 每一小時執行
  },
  "AppInfo": {
    "AppCode": "hmn2-br",
    "RequestIdHeaderName": "X-Request-ID"
  }
}
