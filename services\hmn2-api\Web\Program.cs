﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using NLog.Web;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using Web.ActionFilter;
using Web.Converts;
using Web.Helper;
using Web.Helper.Interfaces;
using Web.Hubs;
using Web.Models.AppSettings;
using Web.Repository;
using Web.Repository.Interface;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Fusion;
using Web.Services.Interfaces;
using Web.Validation;
using Hangfire;
using Hangfire.PostgreSql;

var builder = WebApplication.CreateBuilder(args);

// 加入 Razor 頁面支持
builder.Services.AddRazorPages();

// 配置 NLog 作為日誌提供者
builder.Logging.ClearProviders();
builder.Host.UseNLog();

// 加入 API 探索端點和 Swagger 生成器
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 加入 DbContext 服務
builder.Services.AddDbContextFactory<FusionS3HMNContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("FusionS3HMNConnection")));

// 加入單元工作（UnitOfWork）服務，用於資料庫事務管理
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

// 添加内存缓存服务
builder.Services.AddMemoryCache();

// 加入 HttpContextAccessor 服務，允許在應用中任何地方訪問 HttpContext
builder.Services.AddHttpContextAccessor();

// 註冊AppInfo 設定，從 appsettings.json 加載配置
builder.Services.Configure<AppInfo>(builder.Configuration.GetSection("AppInfo"));

// 註冊通用JSON日期轉換器
builder.Services.AddSingleton(SharedJsonSerializerOptions.Options);

// 註冊日誌服務
builder.Services.AddSingleton<ILogService, LogService>();
builder.Services.AddSingleton<MqttLogService>();
builder.Services.AddSingleton<MqttServerLogService>();

// 添加FusionNet服務
builder.Services.AddScoped<IDeviceService, DeviceServiceNet>();
builder.Services.AddScoped<ILangService, LangServiceNet>();
builder.Services.AddScoped<IStationService, StationServiceNet>();
builder.Services.AddScoped<IPlaneService, PlaneServiceNet>();
builder.Services.AddScoped<ITaskService, TaskServiceNet>();
builder.Services.AddScoped<ICameraService, CameraServiceNet>();
builder.Services.AddScoped<IObjectService, ObjectServiceNet>();
builder.Services.AddScoped<IEventService, EventServiceNet>();
builder.Services.AddScoped<INotificationsService, NotificationsServiceNet>();
builder.Services.AddScoped<ILicenseService, LicenseServiceNet>();
builder.Services.AddScoped<ISystemService, SystemServiceNet>();

// 添加各種服務
builder.Services.AddScoped<IDataAccessService, DataAccessService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IBusinessService, BusinessService>();
builder.Services.AddScoped<IPreferenceService, PreferenceService>();
builder.Services.AddScoped<IConfigurationService, ConfigurationService>();
builder.Services.AddScoped<IMonitorService, MonitorService>();
builder.Services.AddScoped<IFieldService, FieldService>();
builder.Services.AddScoped<IAdminService, AdminService>();
builder.Services.AddScoped<ICredentialService, CredentialService>();
builder.Services.AddScoped<IScheduleService, ScheduleService>();
builder.Services.AddScoped<IScheduleService, ScheduleService>();
builder.Services.AddScoped<INoticeService, NoticeService>();

// 註冊 UtilizationRateBackgroundService
if (builder.Configuration["UtilizationRateScheduleJob:Enable"] != null && builder.Configuration["UtilizationRateScheduleJob:Enable"] == "Y")
{
    builder.Services.AddScoped<UtilizationRateScheduleService>();
}

// 註冊 ScheduleTask 背景服務
if (builder.Configuration["ScheduleTaskBackgroundServiceJob:Enable"] != null && builder.Configuration["ScheduleTaskBackgroundServiceJob:Enable"] == "Y")
{
    builder.Services.AddScoped<ScheduleTaskBackgroundService>();
}

builder.Services.AddScoped<IRequestContextService, RequestContextService>();

builder.Services.AddScoped<CustomValidator>();

builder.Services.AddScoped(serviceProvider =>
{
    var httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();
    var context = serviceProvider.GetRequiredService<FusionS3HMNContext>();
    var auditedTablesTask = context.GlobalSysParas
        .Where(p => p.ParaCode == "AuditedTables")
        .Select(p => p.ParaValue)
        .FirstOrDefaultAsync();
    auditedTablesTask.Wait(); // 注意：這裡使用 Wait() 是為了簡化示例。在實際應用中，應該避免在這裡阻塞，而是使用異步初始化。
    var auditedTables = auditedTablesTask.Result ?? "";
    return new DataAccessLogService(httpContextAccessor, auditedTables);
});

// 添加全局Singleton服務（即一個WEB應用程序中只有一個實例）
builder.Services.AddSingleton<IUtilityService, UtilityService>();
builder.Services.AddSingleton<SignalRGroupService>();
builder.Services.AddSingleton<IDbHelper, DbHelper>();

#region CameraEtl
if (builder.Configuration["CameraEtl:Enable"] != null && builder.Configuration["CameraEtl:Enable"] == "Y")
{
    // 註冊 CameraEtl 設定
    builder.Services.Configure<CameraEtl>(builder.Configuration.GetSection("CameraEtl"));
    // 註冊 Camera Etl 設定
    builder.Services.AddHostedService<CameraEtlBackgroundService>();
}
#endregion

#region MQTT & SignalR
// 註冊 MQTT 設定
builder.Services.Configure<MQTTServerParam>(builder.Configuration.GetSection("MQTTServerParam"));
builder.Services.Configure<MQTTParam>(builder.Configuration.GetSection("MQTTParam"));

// 添加 SignalR 服務
builder.Services.AddSignalR();

// 註冊 MqttBackgroundService 作為單例服務
builder.Services.AddSingleton<MqttBackgroundService>();
// 註冊 MQTT 背景服務
builder.Services.AddHostedService(sp => sp.GetRequiredService<MqttBackgroundService>());

// 註冊 DB Cache 背景服務 (寫入List 無法轉換為IQueryable<T>，所以暫時不使用)
//builder.Services.AddHostedService<CacheBackgroundService>();

// 下面還有一行MapHub<MessageHub>("/messageHub")，做了SignalR的路由設定
#endregion

// Add services to the container.並且關閉回傳的 JSON 屬性名稱大小寫轉換
builder.Services
    .AddControllers(options =>
    {
        //如果在多個地方設置了順序（例如，通過 IOrderedFilter 和屬性參數），ASP.NET Core 會使用最小的 Order 值。
        options.Filters.Add(new TypeFilterAttribute(typeof(LogActionFilter)) { Order = 1 });
        options.Filters.Add(new TypeFilterAttribute(typeof(ValidateModelActionFilter)) { Order = 2 });
        // options.Filters.Add(new TypeFilterAttribute(typeof(ValidationActionFilter)) { Order = 2 });
        options.Filters.Add(new TypeFilterAttribute(typeof(PaginationActionFilter)) { Order = 3 });
        options.Filters.Add(new TypeFilterAttribute(typeof(CustomAuthorizeFilter)) { Order = 4 });
    })
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.Converters.Add(new CustomDateTimeConverter());
    }
);

// 添加跨域
var allowOrigin = builder.Configuration["Cors:AllowOrigin"] != null ? builder.Configuration["Cors:AllowOrigin"] : "*";
if (allowOrigin == "*")
{
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowAll", builder =>
        {
            builder.AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader();
        });
    });
}
else
{
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowFrontend", builder =>
        {
            builder.WithOrigins(allowOrigin)
                   .AllowAnyHeader()
                   .AllowAnyMethod()
                   .AllowCredentials();  // 若前端有用 credentials: include
        });
    });
}

// 檢查請求是否為 API 請求
static bool IsApiRequest(HttpRequest request)
{
    return true;//HMN2 C# Controller 預設就是API，未來如果有需要再調整，所以先保留此方法
    //return request.Path.StartsWithSegments(new PathString("/api"));
}

// 身份验证服务
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme).AddCookie(option =>
{
    //HMN2 預設只做後端API，所以不需要登入頁面，如果未來有需要再調整
    //option.LoginPath = new PathString("/login");//登入頁
    //option.LogoutPath = new PathString("/Auth/Logout");//登出Action

    //用戶頁面停留太久，登入逾期，或Controller中用戶登入時也可設定
    option.ExpireTimeSpan = TimeSpan.FromMinutes(52560000);//365天*1440*100年

    //↓資安建議false，白箱弱掃軟體會要求cookie不能延展效期，這時設false變成絕對逾期時間
    //↓如果你的客戶反應明明一直在使用系統卻容易被自動登出的話，你再設為true(然後弱掃policy請客戶略過此項檢查) 
    option.SlidingExpiration = false;

    // 設置事件處理器
    option.Events = new CookieAuthenticationEvents
    {
        // 當認證失敗並需要重定向到登入頁面時被呼叫
        OnRedirectToLogin = context =>
        {
            // 檢查是否為 API 請求
            if (IsApiRequest(context.Request))
            {
                // 阻止預設的重定向行為
                context.Response.Headers.Location = context.RedirectUri;
                // 返回 HTTP 401 狀態碼
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            }
            else
            {
                // 對於非 API 請求，使用預設的重定向行為
                context.Response.Redirect(context.RedirectUri);
            }

            return Task.CompletedTask;
        }
    };
});

// 添加HttpClient服務 For FusionCoreApi
builder.Services.AddHttpClient("FusionCoreApi").ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();

    if (builder.Configuration["FusionNetParam:SelfCertificateFile"] != null && !string.IsNullOrEmpty(builder.Configuration["FusionNetParam:SelfCertificateFile"]))
    {
        //好像不用加這行
        //handler.ClientCertificateOptions = ClientCertificateOption.Manual;

        // FusionNet 使用自簽證書，所以需要忽略證書驗證
        handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => true;

        // 添加自簽證書
        var certificate = new X509Certificate2(builder.Configuration["FusionNetParam:SelfCertificateFile"]);

        handler.ClientCertificates.Add(certificate);
    }

    return handler;
});

// 添加HttpClient服務 For Test
builder.Services.AddHttpClient("WithCookies")
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        UseCookies = true,
        CookieContainer = new CookieContainer()
    });

//builder.Services.AddSingleton<ITempDataDictionaryFactory, TempDataDictionaryFactory>();
//builder.Services.AddSingleton<ITempDataProvider, CookieTempDataProvider>();

//應用程序添加授權服務。授權是指一旦用戶的身份被系統認證後，決定他們是否有權執行特定操作的過程。
//AddAuthorization 允許你定義授權策略，這些策略可以用來控制對特定資源的訪問權限。
//builder.Services.AddAuthorization();//這行不加也可以，因為我們沒有用到授權

// Add services to the container.
builder.Services.AddControllersWithViews(); // 這行確保了相關MVC服務被註冊到DI容器中

// 增加  OData 配置
builder.Services.AddControllers().AddOData(options => options.Select().Filter().OrderBy());

builder.Services.AddControllers(options =>
{
    options.MaxModelValidationErrors = int.MaxValue; // 設置為整數的最大值，模擬無限制
});

// 增加 Hangfire
builder.Services.AddHangfire(config =>
    config.UsePostgreSqlStorage(builder.Configuration.GetConnectionString("FusionS3HMNConnection")));
builder.Services.AddHangfireServer();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    // 使用嚴格的傳輸安全協議
    app.UseHsts();
}

//// 強制使用 HTTPS
//app.UseHttpsRedirection();

// 確認 FileStorage 目錄已建立
var fileStoragePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FileStorage");

if (!Directory.Exists(fileStoragePath))
{
    Directory.CreateDirectory(fileStoragePath);
}

// 啟用靜態文件服務
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FileStorage")),
    RequestPath = "/FileStorage"
});

if (allowOrigin == "*")
{
    app.UseCors("AllowAll");     // 使用跨域
}
else
{
    app.UseCors("AllowFrontend");    // 使用跨域
}

// 添加日誌中間件
//app.UseMiddleware<RequestLoggingMiddleware>();
// 在這裡添加異常處理中間件
//app.UseMiddleware<ErrorHandlingMiddleware>();

app.UseAuthentication();    // 啟用身份驗證
app.UseAuthorization();     // 啟用授權

// 添加 SignalR 中介軟體
app.MapHub<MessageHub>("/messageHub");

// 添加API路由
app.MapControllerRoute(
    name: "default",
    pattern: "{controller}/{action=Index}/{id?}");

app.UseSwagger();           // 啟用 Swagger
app.UseSwaggerUI();         // 啟用 Swagger UI

app.MapRazorPages();        // 映射 Razor 頁面路由
app.MapControllers();       // 映射控制器路由

// 當路由不匹配 API 或其他路由時，返回 index.html
// 這樣 React 前端路由就可以正常工作
app.MapFallbackToFile("index.html");

// 加上 Hangfire 儀表板 UI（可視化管理介面）
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    Authorization = new[] { new AllowAllAuthorizationFilter() }
});

// 啟動 UtilizationRateScheduleService 排程
if (builder.Configuration["UtilizationRateScheduleJob:Enable"] != null && builder.Configuration["UtilizationRateScheduleJob:Enable"] == "Y")
{
    RecurringJob.RemoveIfExists(builder.Configuration["UtilizationRateScheduleJob:jobName"]);
    RecurringJob.AddOrUpdate<UtilizationRateScheduleService>(
        builder.Configuration["UtilizationRateScheduleJob:jobName"],
        job => job.RunAsync(),
        builder.Configuration["UtilizationRateScheduleJob:CronExpression"],
        TimeZoneInfo.Local
    );
}

// 啟動 ScheduleTaskBackgroundService 排程
if (builder.Configuration["ScheduleTaskBackgroundServiceJob:Enable"] != null && builder.Configuration["ScheduleTaskBackgroundServiceJob:Enable"] == "Y")
{
    RecurringJob.RemoveIfExists(builder.Configuration["ScheduleTaskBackgroundServiceJob:jobName"]);
    RecurringJob.AddOrUpdate<ScheduleTaskBackgroundService>(
        builder.Configuration["ScheduleTaskBackgroundServiceJob:jobName"],
        job => job.RunAsync(),
        builder.Configuration["ScheduleTaskBackgroundServiceJob:CronExpression"],
        TimeZoneInfo.Local
    );
}

// 啟動應用
app.Run();

public class AllowAllAuthorizationFilter : Hangfire.Dashboard.IDashboardAuthorizationFilter
{
    public bool Authorize(Hangfire.Dashboard.DashboardContext context)
    {
        return true; // 開放所有人進入
    }
}
