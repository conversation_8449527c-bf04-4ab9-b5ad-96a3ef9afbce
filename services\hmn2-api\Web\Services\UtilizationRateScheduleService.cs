﻿using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Web.Models.AppSettings;
using Web.Models.Controller;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

public class UtilizationRateScheduleService
{
    private readonly ILogService _logService;
    private readonly IDeviceService _deviceService;
    private readonly ILicenseService _licenseService;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public UtilizationRateScheduleService(
        IDataAccessService dataAccessService,
        ILogService logService,
        IDeviceService deviceService,
        ILicenseService licenseService,
        IServiceScopeFactory serviceScopeFactory)
    {
        _logService = logService;
        _deviceService = deviceService;
        _licenseService = licenseService;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public async Task RunAsync()
    {
        string logActionName = $"{GetType().Name}/ExecuteAsync";

        try
        {
            _logService.Logging("info", logActionName, "", "Start Utilization Rate Background Service");

            using var scope = _serviceScopeFactory.CreateScope();
            var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

            // 取得所有 organization 列表
            List<Organization> organizations = dataAccessService.Fetch<Organization>().ToList();

            foreach (Organization org in organizations)
            {
                if (org.AppCode == null)
                    continue;
                string appCode = org.AppCode;

                // 排程執行邏輯
                await RunTask(appCode);
            }

            _logService.Logging("info", logActionName, "", "Utilization Rate Background Service Execution Finished");
        }
        catch (Exception ex)
        {
            _logService.Logging("info", logActionName, "", String.Format("Utilization Rate Background Service Execution Failed:{0},{1}", ex.ToString(), DateTimeOffset.Now));
        }
    }

    private async Task RunTask(string appCode)
    {
        string logActionName = $"{GetType().Name}/RunTask";

        _logService.Logging("info", logActionName, "", $"[{appCode}] Utilization Rate Task Start");

        using var scope = _serviceScopeFactory.CreateScope();
        var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

        // 取得 GlobalSysPara 中的 UtilizationRate 設定值
        string utilizationRateJsonString = await dataAccessService.Fetch<GlobalSysPara>(x => x.AppCode == appCode && x.ParaCode == "UtilizationRate").Select(x => x.ParaValue).FirstOrDefaultAsync();
        if (string.IsNullOrEmpty(utilizationRateJsonString))
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] UtilizationRate is not set");
            return;
        }

        // 反序列化成 dict 物件
        Dictionary<string, string> utilizationRateDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(utilizationRateJsonString);
        
        string enable = utilizationRateDict["Enable"]; // 
        string initialDate = utilizationRateDict["InitialDate"];
        int retentionMonth = int.Parse(utilizationRateDict["RetentionMonth"]);
        string lastUtilizationDate = utilizationRateDict["LastUtilizationDate"];

        // 刪除超過 retentionMonth 的資料
        DeleteExceedRetentionMonthDataAsync(appCode, retentionMonth);

        // 如果未啟用稼動率統計則不執行
        if (enable != "1")
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] UtilizationRate is not enabled");
            return;
        }

        // 取得稼動率服務授權
        var utilizationServiceList = await _licenseService.GetServiceList(appCode, "active+eq+true+and+code+eq+Utilization");
        if (utilizationServiceList == null || utilizationServiceList.Count == 0 || utilizationServiceList[0].active != true || utilizationServiceList[0].license == null || utilizationServiceList[0].license.isValid != true)
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] Utilization service is not licensed");
            return;
        }

        // 取得所有 Department.TotalAvailableHours 大於 0 且 Department.LastUtilizationDate 小於 lastUtilizationDate  的資料
        var supplementaryDepartmentDataList = string.IsNullOrEmpty(lastUtilizationDate) ? new List<Department>() : await dataAccessService.Fetch<Department>(x => x.AppCode == appCode && x.TotalAvailableHours > 0 && (x.LastUtilizationDate != null && x.LastUtilizationDate < DateTime.Parse(lastUtilizationDate))).AsTracking().ToListAsync();

        // 取得所有 Department.TotalAvailableHours 大於 0 且 Department.LastUtilizationDate 大於等於 lastUtilizationDate  的資料
        var departmentDataList = string.IsNullOrEmpty(lastUtilizationDate) ? new List<Department>() : await dataAccessService.Fetch<Department>(x => x.AppCode == appCode && x.TotalAvailableHours > 0 && (x.LastUtilizationDate != null && x.LastUtilizationDate >= DateTime.Parse(lastUtilizationDate))).AsTracking().ToListAsync();

        // 取得所有 Department.TotalAvailableHours 大於 0 且 Department.LastUtilizationDate 為 null 的資料
        var newDepartmentDataList = await dataAccessService.Fetch<Department>(x => x.AppCode == appCode && x.TotalAvailableHours > 0 && x.LastUtilizationDate == null).AsTracking().ToListAsync();

        // 處理從未執行過稼動率計算的單位
        if (newDepartmentDataList.Count > 0)
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] Found {newDepartmentDataList.Count} new department data");

            var departmentDataDict = newDepartmentDataList.ToDictionary(x => x.DeptCode, x => x);

            // 取得所有 object.utilization 為 true 且 object.usageDepartCode 包含在 supplementaryDepartmentDataList 的 DeptCode 的 ObjectDatum 資料
            var objectDataList = await dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == appCode && x.Utilization == true && newDepartmentDataList.Select(y => y.DeptCode).Contains(x.UsageDepartCode)).ToListAsync();
            if (objectDataList.Count > 0)
            {
                // 取得所有 objectDataList 中的 objectCode 列表
                var objectCodeList = objectDataList.Select(x => x.ObjectCode).Distinct().ToList();
                var objectDataDict = objectDataList.ToDictionary(x => x.ObjectCode, x => x);

                // 取得現在時間作為結束時間 時間字串格式為 yyyy-MM-dd HH:mm:ss
                DateTime now = DateTime.Now;
                string endTimeStr = now.ToString("yyyy-MM-dd HH:mm:ss");

                // 取得設定的初始時間作為起始時間
                DateTime startTime = DateTime.Parse(initialDate);
                string startTimeStr = startTime.ToString("yyyy-MM-dd HH:mm:ss");

                // 取得 EntryExitDurationByHour 資料並寫入 UtilizationRate
                CreateUtilizationRateDataFromEntryExitDurationByHoursAsync(appCode, initialDate, retentionMonth, objectCodeList, objectDataDict, departmentDataDict, startTimeStr, endTimeStr);
            }

            // 更新 Department 的 LastUtilizationDate
            foreach (var departmentData in newDepartmentDataList)
            {
                List<string> updateField = new List<string>();
                updateField.Add("LastUtilizationDate");
                departmentData.LastUtilizationDate = DateTime.Now;

                updateField.Add("ModifyUserAccount");
                departmentData.ModifyUserAccount = "system";

                updateField.Add("ModifyDate");
                departmentData.ModifyDate = DateTime.Now;

                await dataAccessService.UpdateAsync<Department>(departmentData, updateField.ToArray());
            }
        }

        // 處理過去執行過稼動率計算但是中間取消過需要補回資料的單位
        if (supplementaryDepartmentDataList.Count > 0)
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] Found {supplementaryDepartmentDataList.Count} supplementary department data");

            // 依序處理每一筆 supplementaryDepartmentDataList
            foreach (var departmentData in supplementaryDepartmentDataList)
            {
                var departmentDataDict = new Dictionary<string?, Department> { { departmentData.DeptCode, departmentData } };

                // 取得所有 object.utilization 為 true 且 object.usageDepartCode 等於 departmentData 的 DeptCode 的 ObjectDatum 資料
                var objectDataList = await dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == appCode && x.Utilization == true && x.UsageDepartCode == departmentData.DeptCode).ToListAsync();
                if (objectDataList.Count > 0)
                {
                    // 取得所有 objectDataList 中的 objectCode 列表
                    var objectCodeList = objectDataList.Select(x => x.ObjectCode).Distinct().ToList();
                    var objectDataDict = objectDataList.ToDictionary(x => x.ObjectCode, x => x);

                    // 取得現在時間作為時間結束時間字串 時間格式為 yyyy-MM-dd HH:mm:ss
                    DateTime now = DateTime.Now;
                    string endTimeStr = now.ToString("yyyy-MM-dd HH:mm:ss");

                    // 取得上一次執行的時間作為起始時間
                    DateTime startTime = departmentData.LastUtilizationDate.Value;
                    string startTimeStr = startTime.ToString("yyyy-MM-dd HH:mm:ss");

                    // 取得 EntryExitDurationByHour 資料並寫入 UtilizationRate
                    CreateUtilizationRateDataFromEntryExitDurationByHoursAsync(appCode, initialDate, retentionMonth, objectCodeList, objectDataDict, departmentDataDict, startTimeStr, endTimeStr);
                }

                // 更新 Department 的 LastUtilizationDate
                List<string> updateField = new List<string>();
                updateField.Add("LastUtilizationDate");
                departmentData.LastUtilizationDate = DateTime.Now;

                updateField.Add("ModifyUserAccount");
                departmentData.ModifyUserAccount = "system";

                updateField.Add("ModifyDate");
                departmentData.ModifyDate = DateTime.Now;

                await dataAccessService.UpdateAsync<Department>(departmentData, updateField.ToArray());
            }
        }

        // 處理上次執行過稼動率計算的單位
        if (departmentDataList.Count > 0)
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] Found {departmentDataList.Count} department data");

            // 依序處理每一筆 departmentDataList
            foreach (var departmentData in departmentDataList)
            {
                var departmentDataDict = new Dictionary<string?, Department> { { departmentData.DeptCode, departmentData } };

                // 取得所有 object.utilization 為 true 且 object.usageDepartCode 等於 departmentData 的 DeptCode 的 ObjectDatum 資料
                var objectDataList = await dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == appCode && x.Utilization == true && x.UsageDepartCode == departmentData.DeptCode).ToListAsync();
                if (objectDataList.Count > 0)
                {
                    // 取得所有 objectDataList 中的 objectCode 列表
                    var objectCodeList = objectDataList.Select(x => x.ObjectCode).Distinct().ToList();
                    var objectDataDict = objectDataList.ToDictionary(x => x.ObjectCode, x => x);

                    // 取得現在時間作為時間結束時間字串 時間格式為 yyyy-MM-dd HH:mm:ss
                    DateTime now = DateTime.Now;
                    string endTimeStr = now.ToString("yyyy-MM-dd HH:mm:ss");

                    // 取得上一次執行的時間作為起始時間
                    DateTime startTime = departmentData.LastUtilizationDate.Value;
                    string startTimeStr = startTime.ToString("yyyy-MM-dd HH:mm:ss");

                    // 取得 EntryExitDurationByHour 資料並寫入 UtilizationRate
                    CreateUtilizationRateDataFromEntryExitDurationByHoursAsync(appCode, initialDate, retentionMonth, objectCodeList, objectDataDict, departmentDataDict, startTimeStr, endTimeStr);
                }

                // 更新 Department 的 LastUtilizationDate
                List<string> updateField = new List<string>();
                updateField.Add("LastUtilizationDate");
                departmentData.LastUtilizationDate = DateTime.Now;

                updateField.Add("ModifyUserAccount");
                departmentData.ModifyUserAccount = "system";

                updateField.Add("ModifyDate");
                departmentData.ModifyDate = DateTime.Now;

                await dataAccessService.UpdateAsync<Department>(departmentData, updateField.ToArray());
            }
        }

        // 更新 GlobalSysPara 中的 LastUtilizationDate
        var globalSysPara = await dataAccessService.Fetch<GlobalSysPara>(x => x.AppCode == appCode && x.ParaCode == "UtilizationRate").AsTracking().FirstAsync();
        globalSysPara.ParaValue = JsonConvert.SerializeObject(new Dictionary<string, string>
        {
            { "Enable", "1" },
            { "InitialDate", initialDate },
            { "RetentionMonth", retentionMonth.ToString() },
            { "LastUtilizationDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") }
        });
        await dataAccessService.UpdateAsync(globalSysPara, new[] { "ParaValue" });

        _logService.Logging("info", logActionName, "", $"[{appCode}] Utilization Rate Task End");
    }

    private async Task CreateUtilizationRateDataFromEntryExitDurationByHoursAsync(string appCode, string initialDate, int retentionMonth, List<string> objectCodeList, Dictionary<string, ObjectDatum> objectDataDict, Dictionary<string?, Department> departmentDataDict, string startTimeStr, string endTimeStr)
    {
        string logActionName = $"{GetType().Name}/CreateUtilizationRateDataFromEntryExitDurationByHoursAsync";

        using var scope = _serviceScopeFactory.CreateScope();
        var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

        // 取得 EntryExitDurationByHour 資料
        var inputParam = new TrajectoryInput
        {
            search = $"positionTime between '{startTimeStr},{endTimeStr}' and object.code in {string.Join(",", objectCodeList)}",
            inlinecount = true,
            size = 1,
            sort = "positionTime,desc"
        };

        inputParam.size = await _deviceService.GetEntryExitDurationByHourCount(appCode, inputParam);
        if (inputParam.size == 0)
        {
            _logService.Logging("info", logActionName, "", $"[{appCode}] No EntryExitDurationByHour data between {startTimeStr} and {endTimeStr}===========================");
            return;
        }

        var allEntryExitDurationByHourList = inputParam.size == 0 ? [] : await _deviceService.GetEntryExitDurationByHourList(appCode, inputParam);

        // 將 entryExitDurationByHourList 依照 objectCode, stationSid, devicePid 分組
        var groupedEntryExitDurationByHourList = allEntryExitDurationByHourList.GroupBy(x => new { <EMAIL>, x.station.sid, x.device.pid }).ToList();

        // 取得所有 allEntryExitDurationByHourList 中 station.sid 對應的 deviceData
        var stationSidList = allEntryExitDurationByHourList.Select(x => x.station.sid).Distinct().ToList();
        var stationDataList = await dataAccessService.Fetch<Station>(x => x.AppCode == appCode && stationSidList.Contains(x.SID)).ToListAsync();

        // 取得所有 stationDataList 中的 RegionCode 對應的 locationData
        var regionCodeList = stationDataList.Select(x => x.RegionCode).Distinct().ToList();
        var locationDataList = await dataAccessService.Fetch<Location>(x => x.AppCode == appCode && regionCodeList.Contains(x.LocCode)).ToListAsync();
        var locationDataDict = locationDataList.ToDictionary(x => x.LocCode, x => x);
        
        // 建立 objectCode+Pid 與 utilizationRate 的 dict
        var objectPidUtilizationRatesDict = new Dictionary<string, List<UtilizationRate>>();

        // 依序處理每一組資料
        foreach (var group in groupedEntryExitDurationByHourList)
        {
            // 取得 group 中的第一筆資料
            var firstEntryExitDurationByHour = group.First();

            // 取得 group 中的所有資料
            var entryExitDurationByHourList = group.ToList();

            // 取得 group 中的第一筆資料的 objectCode
            string objectCode = <EMAIL>;
            var objectData = objectDataDict[objectCode];
            string groupCode = objectData.GroupCode;
            string pid = firstEntryExitDurationByHour.device.pid;

            // 取得 group 中的第一筆資料的 stationSid
            string stationSid = firstEntryExitDurationByHour.station.sid;
            var stationData = stationDataList.FirstOrDefault(x => x.SID == stationSid);

            if (objectData.Utilization == false)
            {
                _logService.Logging("info", logActionName, "", $"[{appCode}] ObjectCode:{objectCode} is not enabled for utilization rate");
                continue;
            }

            string usageDepartCode = objectData.UsageDepartCode;
            var departmentData = departmentDataDict[usageDepartCode];
            int totalAvailableHours = departmentData.TotalAvailableHours.HasValue ? departmentData.TotalAvailableHours.Value : 0;
            if (totalAvailableHours == 0)
            {
                _logService.Logging("info", logActionName, "", $"[{appCode}] DepartmentCode:{objectData.UsageDepartCode} has no available hours");
                continue;
            }

            if (stationData == null)
            {
                _logService.Logging("info", logActionName, "", $"[{appCode}] StationCode:{stationSid} is not found");
                continue;
            }

            if (string.IsNullOrEmpty(stationData.RegionCode))
            {
                _logService.Logging("info", logActionName, "", $"[{appCode}] StationCode:{stationSid} has no region code");
                continue;
            }

            if (locationDataDict.ContainsKey(stationData.RegionCode) == false)
            {
                _logService.Logging("info", logActionName, "", $"[{appCode}] LocationCode:{stationData.RegionCode} is not found");
                continue;
            }

            var locationData = locationDataDict[stationData.RegionCode];
            if (locationData.Utilization == false)
            {
                _logService.Logging("info", logActionName, "", $"[{appCode}] LocationCode:{stationData.RegionCode} is not enabled for utilization rate");
                continue;
            }

            // 檢查每筆 entryExitDurationByHour 的 positionTime 的星期是否在非稼動設定中
            var nonUtilizationWeekly = departmentData.NonUtilizationWeekly; // 0,1,2,3,4,5,6
            if (!string.IsNullOrEmpty(nonUtilizationWeekly))
            {
                entryExitDurationByHourList = entryExitDurationByHourList.Where(x => !nonUtilizationWeekly.Contains(((int)x.positionTime.DayOfWeek).ToString())).ToList();
            }

            // 檢查每筆 entryExitDurationByHour 的 positionTime 的時間小時是否在可使用稼動時間區間設定中，區間左閉右開
            if (departmentData.AvailableStartTime.HasValue && departmentData.AvailableEndTime.HasValue)
            {
                int availableStartTime = departmentData.AvailableStartTime.Value;
                int availableEndTime = departmentData.AvailableEndTime.Value;
                entryExitDurationByHourList = entryExitDurationByHourList.Where(x => x.positionTime.Hour >= availableStartTime && x.positionTime.Hour < availableEndTime).ToList();
            }

            // 將 entryExitDurationByHourList 依照 positionTime 進行排序
            entryExitDurationByHourList = entryExitDurationByHourList.OrderBy(x => x.positionTime).ToList();

            // 將 entryExitDurationByHourList 依照 positionTime 的日期進行分組
            var groupedEntryExitDurationByHourListByDate = entryExitDurationByHourList.GroupBy(x => x.positionTime.ToString("yyyy-MM-dd")).ToList();

            string objectCodePidkey = $"{objectCode},{pid}";

            foreach (var groupByDate in groupedEntryExitDurationByHourListByDate)
            {
                // 取得 groupByDate 的日期
                string positionDate = groupByDate.Key;

                // 取得 groupByDate 中的所有資料
                var entryExitDurationByHourListByDate = groupByDate.ToList();

                // 計算總秒數
                double totalSec = entryExitDurationByHourListByDate.Sum(x => x.stayDuration);

                if (objectPidUtilizationRatesDict.ContainsKey(objectCodePidkey))
                {
                    // 在 objectPidUtilizationRatesDict[objectCodePidkey] 中找到 UtilizationDay 的日期等於 positionDate 的資料
                    var utilizationRateData = objectPidUtilizationRatesDict[objectCodePidkey].FirstOrDefault(x => x.UtilizationDay.ToString("yyyy-MM-dd") == positionDate);
                    if (utilizationRateData != null)
                    {
                        // 累加總秒數
                        utilizationRateData.DayTotalSec += (int)totalSec;

                        // 重新計算稼動率
                        utilizationRateData.UtilizationRateValue = (double)utilizationRateData.DayTotalSec / (60 * 60 * totalAvailableHours);
                    }
                    else
                    {
                        // 新增新的 UtilizationRate 資料
                        objectPidUtilizationRatesDict[objectCodePidkey].Add(new UtilizationRate
                        {
                            AppCode = appCode,
                            ObjectCode = objectCode,
                            UsageDepartCode = usageDepartCode,
                            GroupCode = groupCode,
                            Pid = pid,
                            UtilizationDay = DateTime.Parse(positionDate),
                            DayTotalSec = (int)totalSec,
                            UtilizationRateValue = (double)totalSec / (60 * 60 * totalAvailableHours),
                            CreateUserAccount = "system",
                            CreateDate = DateTime.Now
                        });
                    }
                }
                else
                {
                    // 新增新的 UtilizationRate 資料
                    objectPidUtilizationRatesDict[objectCodePidkey] = new List<UtilizationRate>
                    {
                        new UtilizationRate
                        {
                            AppCode = appCode,
                            ObjectCode = objectCode,
                            UsageDepartCode = usageDepartCode,
                            GroupCode = groupCode,
                            Pid = pid,
                            UtilizationDay = DateTime.Parse(positionDate),
                            DayTotalSec = (int)totalSec,
                            UtilizationRateValue = (double)totalSec / (60 * 60 * totalAvailableHours),
                            CreateUserAccount = "system",
                            CreateDate = DateTime.Now
                        }
                    };
                }
            }
        }

        // 將資料寫入資料庫
        if (objectPidUtilizationRatesDict.Values.Count > 0)
        {
            await dataAccessService.CreateRangeAsync(objectPidUtilizationRatesDict.Values.SelectMany(x => x));
        }
    }

    private async Task DeleteExceedRetentionMonthDataAsync(string appCode, int retentionMonth)
    {
        string logActionName = $"{GetType().Name}/DeleteExceedRetentionMonthDataAsync";
        _logService.Logging("info", logActionName, "", $"[{appCode}] Delete exceed retention month data");

        using var scope = _serviceScopeFactory.CreateScope();
        var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

        DateTime deleteDate = DateTime.Now.AddMonths(-retentionMonth);
        await dataAccessService.DeleteAsync<UtilizationRate>(x => x.AppCode == appCode && x.UtilizationDay < deleteDate);
    }
}
