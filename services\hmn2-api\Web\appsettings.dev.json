{
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.SignalR": "Warning",
      "Microsoft.AspNetCore.Http.Connections.Internal.Transports.WebSocketsTransport": "Warning",
      "Microsoft.EntityFrameworkCore.ChangeTracking": "Warning",
      "Microsoft.EntityFrameworkCore.Database": "Warning"
    }
  },
  "ConnectionStrings": {
    "FusionS3HMNConnection": "server=************;port=8260;database=FusionS3HMN;uid=postgres;pwd=*************;"
  },
  "FusionNetParam": {
    "ApiUrl": "http://************:8080",
    "ApiVersion": "v3"
  },
  "MQTTParam": {
    "HostIp": "************",
    "HostPort": "8204", //default: if TLS 8885 else 8204 
    "UseTLS": "N", //default: if TLS Y else N 
	  "Enable": "Y", //default: Y
    "Timeout": 5000,
    "SelfCertificateFile": ""
  },
  "MQTTServerParam": {
    "HostPort": "8112",
    "ConnectionBacklog": 100,
    "MaxPendingMessagesPerClient": 1000
  },
  "Cors": {
    "AllowOrigin": "*"
  },
  "UtilizationRateScheduleJob": {
    "Enable": "Y",
    "jobName": "UtilizationRateScheduleJob",
    "CronExpression": "0 5 * * *" // 每天 05:00 執行
  },
  "ScheduleTaskBackgroundServiceJob": {
    "Enable": "Y",
    "jobName": "ScheduleTaskBackgroundServiceJob",
    "CronExpression": "0 0 * * * *" // 每一小時執行
  },
  "CameraEtl": {
    "Enable": "Y",
    "WorkingDirectory": "Etls",
    "WorkingFileName": "fnc-application-hmn-camera-etl.jar"
  },
  "AppInfo": {
    "AppCode": "hmn2-dev",
    "RequestIdHeaderName": "X-Request-ID"
  }
}