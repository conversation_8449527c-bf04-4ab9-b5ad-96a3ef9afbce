﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Department
{
    public int Id { get; set; }

    public bool? Enable { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? SectorCode { get; set; }

    public string? DeptCode { get; set; }

    public string? CustomDeptCode { get; set; }

    public string? DeptName { get; set; }

    public string? Supervisor { get; set; }

    public string? DeptEmail { get; set; }

    public string? OrgType { get; set; }

    public bool? IsManagedDept { get; set; }

    public bool? IsUsageDept { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? BuildingCode { get; set; }

    public string? PlaneCode { get; set; }

    public int? TotalAvailableHours { get; set; }

    public DateTime? LastUtilizationDate { get; set; }

    public string? NonUtilizationWeekly { get; set; }

    public int? AvailableStartTime { get; set; }

    public int? AvailableEndTime { get; set; }
}
